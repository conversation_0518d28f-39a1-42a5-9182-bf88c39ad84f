2025-04-15 05:18:32,678265000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][Functions#101][1087] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] #1 functions str
2025-04-15 05:18:32,678458000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][Functions#103][Functions#101:Functions#101:1087:0:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] #2 executeDataSendingFlow str
2025-04-15 05:18:32,678571000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingLib#103][Functions#101:Functions#103:1087:0:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$1 dataSendingFlow str
2025-04-15 05:18:32,678696000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingResolver#101][Functions#101:DataSendingLib#103:1087:0:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$2 resolver str
2025-04-15 05:18:32,680570000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][NotificationResolver#103][Functions#101:DataSendingResolver#101:1089:2:2] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$2 resolveSendTarget str
2025-04-15 05:18:32,680714000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#101][Functions#101:NotificationResolver#103:1089:2:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch str. internalUserId=66
2025-04-15 05:18:32,714857000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#102][Functions#101:AuthUserSettingRepository#101:1123:36:34] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch fin. internalUserId=66
2025-04-15 05:18:32,715610000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][NotificationResolver#104][Functions#101:AuthUserSettingRepository#102:1124:37:1] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$2 resolveSendTarget fin
2025-04-15 05:18:32,717171000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][NotificationResolver#103][Functions#101:NotificationResolver#104:1126:39:2] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$2 resolveSendTarget str
2025-04-15 05:18:32,717340000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#101][Functions#101:NotificationResolver#103:1126:39:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch str. internalUserId=54
2025-04-15 05:18:32,730250000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#102][Functions#101:AuthUserSettingRepository#101:1139:52:13] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch fin. internalUserId=54
2025-04-15 05:18:32,730759000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#101][Functions#101:AuthUserSettingRepository#102:1139:52:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch str. internalUserId=56
2025-04-15 05:18:32,742325000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#102][Functions#101:AuthUserSettingRepository#101:1151:64:12] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch fin. internalUserId=56
2025-04-15 05:18:32,742765000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#101][Functions#101:AuthUserSettingRepository#102:1151:64:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch str. internalUserId=59
2025-04-15 05:18:32,753266000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#102][Functions#101:AuthUserSettingRepository#101:1162:75:11] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch fin. internalUserId=59
2025-04-15 05:18:32,753720000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#101][Functions#101:AuthUserSettingRepository#102:1162:75:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch str. internalUserId=61
2025-04-15 05:18:32,766551000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][AuthUserSettingRepository#102][Functions#101:AuthUserSettingRepository#101:1175:88:13] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1 fetch fin. internalUserId=61
2025-04-15 05:18:32,767006000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][NotificationResolver#104][Functions#101:AuthUserSettingRepository#102:1175:88:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$2 resolveSendTarget fin
2025-04-15 05:18:32,767150000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingResolver#102][Functions#101:NotificationResolver#104:1176:89:1] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$2 resolver fin
2025-04-15 05:18:32,767223000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingFlow#101][Functions#101:DataSendingResolver#102:1176:89:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$4 doExecute str total=2
2025-04-15 05:18:32,767290000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingFlow#103][Functions#101:DataSendingFlow#101:1176:89:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$5 doExecute str count=1
2025-04-15 05:18:32,767423000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][GetWarningMessageRule#101][Functions#101:DataSendingFlow#103:1176:89:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$6 resolveMessage str
2025-04-15 05:18:32,767588000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1001][Functions#101:GetWarningMessageRule#103:1176:89:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db connection create str
2025-04-15 05:18:32,767710000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1003][Functions#101:ConnectionWithSqlDb#1001:1176:89:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db token create str
2025-04-15 05:18:32,770083000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-15 05:18:35,142397000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	64	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-15 05:18:35,143090000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1004][Functions#101:ConnectionWithSqlDb#1003:3551:2464:2375] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db token create fin
2025-04-15 05:18:35,212651000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1002][Functions#101:ConnectionWithSqlDb#1004:3613:2526:62] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db connection create fin
2025-04-15 05:18:35,218291000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1001][Functions#101:GetWarningMessageRule#105:3627:2540:2] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db connection create str
2025-04-15 05:18:35,218457000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1003][Functions#101:ConnectionWithSqlDb#1001:3627:2540:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db token create str
2025-04-15 05:18:35,220651000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-15 05:18:36,364664000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	67	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-15 05:18:36,365631000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1004][Functions#101:ConnectionWithSqlDb#1003:4774:3687:1147] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db token create fin
2025-04-15 05:18:36,409762000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1002][Functions#101:ConnectionWithSqlDb#1004:4818:3731:44] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db connection create fin
2025-04-15 05:18:51,859276000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][GetWarningMessageRule#102][Functions#101:GetWarningMessageRule#106:20268:19181:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$6 resolveMessage fin
2025-04-15 05:18:51,860360000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingFlow#104][Functions#101:GetWarningMessageRule#102:20269:19182:1] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$5 doExecute fin count=1
2025-04-15 05:18:51,860496000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingFlow#103][Functions#101:DataSendingFlow#104:20269:19182:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$5 doExecute str count=2
2025-04-15 05:18:51,860677000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][GetWarningMessageRule#101][Functions#101:DataSendingFlow#103:20269:19182:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$6 resolveMessage str
2025-04-15 05:18:51,860854000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1001][Functions#101:GetWarningMessageRule#103:20269:19182:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db connection create str
2025-04-15 05:18:51,860979000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1003][Functions#101:ConnectionWithSqlDb#1001:20269:19182:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db token create str
2025-04-15 05:18:51,863146000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-15 05:18:52,263014000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	69	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-15 05:18:52,263911000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1004][Functions#101:ConnectionWithSqlDb#1003:20672:19585:403] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db token create fin
2025-04-15 05:18:52,311138000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1002][Functions#101:ConnectionWithSqlDb#1004:20720:19633:48] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db connection create fin
2025-04-15 05:18:52,315512000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1001][Functions#101:GetWarningMessageRule#105:20724:19637:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db connection create str
2025-04-15 05:18:52,315657000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1003][Functions#101:ConnectionWithSqlDb#1001:20724:19637:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db token create str
2025-04-15 05:18:52,317820000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-15 05:18:54,600714000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	72	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-15 05:18:54,601626000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1004][Functions#101:ConnectionWithSqlDb#1003:23010:21923:2286] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db token create fin
2025-04-15 05:18:54,649021000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][ConnectionWithSqlDb#1002][Functions#101:ConnectionWithSqlDb#1004:23057:21970:47] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] db connection create fin
2025-04-15 05:18:54,652592000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][GetWarningMessageRule#102][Functions#101:GetWarningMessageRule#106:23061:21974:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$6 resolveMessage fin
2025-04-15 05:18:54,654698000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][MyMazdaAppInboxSendRule#101][Functions#101:MyMazdaAppInboxSendRule#500:23063:21976:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$7 registerInbox str
2025-04-15 05:18:54,683136000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][MyMazdaAppInboxSendRule#102][Functions#101:MyMazdaAppInboxSendRule#101:23091:22004:28] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$7 registerInbox fin
2025-04-15 05:18:54,683476000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendRule#101][Functions#101:DataSendRule#500:23092:22005:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$7 dataSend str
2025-04-15 05:18:54,753683000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendRule#102][Functions#101:DataSendRule#101:23162:22075:70] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$7 dataSend fin
2025-04-15 05:18:54,753855000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendRule#101][Functions#101:DataSendRule#500:23162:22075:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$7 dataSend str
2025-04-15 05:18:54,844414000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendRule#102][Functions#101:DataSendRule#101:23253:22166:91] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$7 dataSend fin
2025-04-15 05:18:54,844573000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingFlow#104][Functions#101:DataSendRule#102:23253:22166:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$5 doExecute fin count=2
2025-04-15 05:18:54,844660000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingFlow#102][Functions#101:DataSendingFlow#104:23253:22166:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$4 doExecute fin
2025-04-15 05:18:54,844766000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][DataSendingLib#104][Functions#101:DataSendingFlow#102:23253:22166:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] 1-#$1 dataSendingFlow fin
2025-04-15 05:18:54,845150000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][Functions#104][Functions#101:DataSendingLib#104:23254:22167:1] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] #2 executeDataSendingFlow fin
2025-04-15 05:18:54,845233000	I	apf-dsn-idle-01-b75f78cd8-5t4pp	apf-dsn-flow	63	18	[5ae87552-d4c7-4fa0-be64-8a868adbac1b][Functions#102][Functions#101:Functions#104:23254:22167:0] [60_2_3:20:* apf-dsn-idle-01-b75f78cd8-5t4pp] #1 functions fin
