2025-04-16 08:49:52,467574000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Functions#101][9903] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] #1 functions str
2025-04-16 08:49:52,470451000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Functions#103][Functions#101:Functions#101:9912:9:9] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] #2 executeDataSendingFlow str
2025-04-16 08:49:52,519445000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendingLib#103][Functions#101:Functions#103:9963:60:51] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$1 dataSendingFlow str
2025-04-16 08:49:52,520955000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendingResolver#101][Functions#101:DataSendingLib#103:9964:61:1] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$2 resolver str
2025-04-16 08:49:52,564364000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendingResolver#102][Functions#101:DataSendingResolver#101:10008:105:44] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$2 resolver fin
2025-04-16 08:49:52,564884000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendingFlow#101][Functions#101:DataSendingResolver#102:10008:105:0] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$4 doExecute str total=1
2025-04-16 08:49:52,565004000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendingFlow#103][Functions#101:DataSendingFlow#101:10008:105:0] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$5 doExecute str count=1
2025-04-16 08:49:52,568602000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendRule#101][Functions#101:DataSendRule#500:10012:109:0] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$7 dataSend str
2025-04-16 08:49:53,294345000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendRule#102][Functions#101:DataSendRule#101:10738:835:726] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$7 dataSend fin
2025-04-16 08:49:53,299290000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][TscHttpResponseSaveRule#101][Functions#101:DataSendRule#102:10743:840:5] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$8 updateReqInfo str
2025-04-16 08:49:53,328390000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][ConnectionWithSqlDb#1001][Functions#101:TscHttpResponseSaveRule#101:10772:869:29] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] db connection create str
2025-04-16 08:49:53,342973000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][ConnectionWithSqlDb#1003][Functions#101:ConnectionWithSqlDb#1001:10786:883:14] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] db token create str
2025-04-16 08:49:53,466844000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-16 08:49:56,419840000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	32	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-16 08:49:56,420484000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][ConnectionWithSqlDb#1004][Functions#101:ConnectionWithSqlDb#1003:13864:3961:3078] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] db token create fin
2025-04-16 08:49:56,569914000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][ConnectionWithSqlDb#1002][Functions#101:ConnectionWithSqlDb#1004:14013:4110:149] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] db connection create fin
2025-04-16 08:49:56,649651000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][TscHttpResponseSaveRule#102][Functions#101:CommonRepository#104:14093:4190:13] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$8 updateReqInfo fin
2025-04-16 08:49:56,649884000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendingFlow#104][Functions#101:TscHttpResponseSaveRule#102:14093:4190:0] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$5 doExecute fin count=1
2025-04-16 08:49:56,649998000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendingFlow#102][Functions#101:DataSendingFlow#104:14093:4190:0] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$4 doExecute fin
2025-04-16 08:49:56,651245000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][DataSendingLib#104][Functions#101:DataSendingFlow#102:14095:4192:2] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] 1-#$1 dataSendingFlow fin
2025-04-16 08:49:56,651994000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Functions#104][Functions#101:DataSendingLib#104:14095:4192:0] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] #2 executeDataSendingFlow fin
2025-04-16 08:49:56,652124000	I	apf-dsn-idle-01-b75f78cd8-bxczm	apf-dsn-flow	62	17	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Functions#102][Functions#101:Functions#104:14096:4193:1] [190_2_1:20:* apf-dsn-idle-01-b75f78cd8-bxczm] #1 functions fin
