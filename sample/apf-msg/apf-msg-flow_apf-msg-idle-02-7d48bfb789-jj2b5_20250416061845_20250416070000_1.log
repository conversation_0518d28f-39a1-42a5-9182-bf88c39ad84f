2025-04-16 06:18:41,803073000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][Functions#1002][5153] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] run str data={"Vin":"JMZDR1WBJ00222009","FileName":"250416_061819_40.ofn","ReceiveDateTime":"2025-04-16T06:18:26Z","FileData":"00002504160539438000000025041606181980000001000025041605394480002504160618188000000000FFFFFF00010001CDCC7B00000000005100000002"} optional={"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"internalVin":1,"vin":"JMZDR1WBJ00222009","region":"MME","blankVin":"JMZDR1WBJ00222009","vehicleUser":[{"firstName":"UserBE","lastName":"TestBE","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":0,"locale":"en-GB","internalUserId":55,"option":"{}"},{"firstName":"UserBI","lastName":"TestBI","country":"DE","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":1,"locale":"en-DE","internalUserId":59,"option":"{}"}]}}
2025-04-16 06:18:41,811032000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][LatestVehiclePersistent#1001][0] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] LatestVehiclePersistent str
2025-04-16 06:18:41,813814000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageFlow#1001][Functions#1002:Functions#1001:5170:17:9] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] execute str
2025-04-16 06:18:41,815399000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageStatResolver#1001][Functions#1002:MessageFlow#1001:5172:19:2] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] execute str
2025-04-16 06:18:41,815513000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][LatestVehiclePersistent#1001][0] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] LatestVehiclePersistent str
2025-04-16 06:18:41,951348000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][ConnectionWithSqlDb#1001][Functions#1002:MessageStatResolver#1001:5308:155:136] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] db connection create str
2025-04-16 06:18:41,980726000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][ConnectionWithSqlDb#1003][Functions#1002:ConnectionWithSqlDb#1001:5337:184:29] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] db token create str
2025-04-16 06:18:42,098310000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-16 06:18:44,521893000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	26	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-16 06:18:44,522568000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][ConnectionWithSqlDb#1004][Functions#1002:ConnectionWithSqlDb#1003:7879:2726:2542] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] db token create fin
2025-04-16 06:18:44,663432000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][ConnectionWithSqlDb#1002][Functions#1002:ConnectionWithSqlDb#1004:8020:2867:141] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] db connection create fin
2025-04-16 06:18:44,666279000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageStatResolver#1001][Functions#1002:ConnectionWithSqlDb#1002:8023:2870:3] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] doFetch_fetch str
2025-04-16 06:18:44,687543000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:2872:2872:2872] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] fetch-query str
2025-04-16 06:18:44,707892000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:2892:2892:20] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] fetch-query fin
2025-04-16 06:18:44,720716000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageStatResolver#1001][Functions#1002:MessageStatResolver#1001:8077:2924:54] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] doFetch_fetch fin
2025-04-16 06:18:44,721891000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageStatResolver#1001][Functions#1002:MessageStatResolver#1001:8078:2925:1] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] receive notice toNotRetry
2025-04-16 06:18:44,721993000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageStatResolver#1001][Functions#1002:MessageStatResolver#1001:8078:2925:0] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] execute fin
2025-04-16 06:18:44,727096000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][BizLogicLauncher#1001][Functions#1002:MessageStatResolver#1001:8084:2931:6] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] mazda.tk2.messagingFoundation.server.imp.biz._220_2_6.Biz_220_2_6 str
2025-04-16 06:18:44,727497000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][BizLogicLauncher#1001][Functions#1002:BizLogicLauncher#1001:8084:2931:0] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] mazda.tk2.messagingFoundation.server.imp.biz._220_2_6.Biz_220_2_6 fin
2025-04-16 06:18:44,728134000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][ConnectionWithSqlDb#1001][Functions#1002:BizLogicLauncher#1001:8085:2932:1] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] db connection create str
2025-04-16 06:18:44,728285000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][ConnectionWithSqlDb#1003][Functions#1002:ConnectionWithSqlDb#1001:8085:2932:0] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] db token create str
2025-04-16 06:18:44,738667000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-16 06:18:45,007448000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	35	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-16 06:18:45,008292000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][ConnectionWithSqlDb#1004][Functions#1002:ConnectionWithSqlDb#1003:8364:3211:279] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] db token create fin
2025-04-16 06:18:45,059103000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][ConnectionWithSqlDb#1002][Functions#1002:ConnectionWithSqlDb#1004:8416:3263:52] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] db connection create fin
2025-04-16 06:18:45,074220000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageFlow#1001][Functions#1002:ConnectionWithSqlDb#1002:8431:3278:15] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] doDetach str
2025-04-16 06:18:45,074362000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageFlow#1001][Functions#1002:MessageFlow#1001:8431:3278:0] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] doDetach_update str
2025-04-16 06:18:45,074568000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:3259:3259:367] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] checkConflict-query str
2025-04-16 06:18:45,076497000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:3261:3261:2] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] checkConflict-query fin
2025-04-16 06:18:45,082565000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:3267:3267:6] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] updateAll-query str
2025-04-16 06:18:45,086078000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:3270:3270:3] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] updateAll-query fin
2025-04-16 06:18:45,086240000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageFlow#1001][Functions#1002:MessageFlow#1001:8443:3290:12] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] doDetach_update fin
2025-04-16 06:18:45,086323000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageFlow#1001][Functions#1002:MessageFlow#1001:8443:3290:0] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] doDetach fin
2025-04-16 06:18:45,093887000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageSender#1001][Functions#1002:MessageFlow#1001:8450:3297:7] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] execute_DataSender str
2025-04-16 06:18:45,094122000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageSender#1001][Functions#1002:MessageSender#1001:8451:3298:1] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] execute_DataSender fin
2025-04-16 06:18:45,094218000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageSender#1001][Functions#1002:MessageSender#1001:8451:3298:0] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] execute_DataProviding str
2025-04-16 06:18:45,627994000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageSender#1001][Functions#1002:MessageSender#1001:8984:3831:533] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] execute_DataProviding fin
2025-04-16 06:18:45,628189000	I	apf-msg-idle-02-7d48bfb789-jj2b5	apf-msg-flow	63	17	[71a562ca-744f-4647-8301-85d55cd035d4][MessageFlow#1001][Functions#1002:MessageSender#1001:8985:3832:1] [220_2_6:20:* apf-msg-idle-02-7d48bfb789-jj2b5] execute fin
