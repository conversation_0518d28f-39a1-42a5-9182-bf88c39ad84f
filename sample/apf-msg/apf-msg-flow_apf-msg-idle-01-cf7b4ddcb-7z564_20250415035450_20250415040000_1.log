2025-04-15 03:54:45,664975000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][Functions#1002][5196] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] run str data={"Vin":"JMZDR1WBJ00222009","VehicleStatusData":{"OccurrenceTime":"2025-04-15T03:52:06Z","TransmissionFactor":32,"DcmNumber":"9999999999900000","BsId":"FFFF","SId":"FFFF","NId":"FFFF","DcmDormantDatetime":"2025-04-24T03:51:01Z","NotificationCategory":33,"PositionInfoCategory":4,"PositionInfo":{"AcquisitionDatetime":"2025-04-15T03:51:18Z","Longitude":132.499792,"Latitude":34.380417,"UsbPositionAccuracy":345,"GeodeticReferenceSystem":0},"OccurrenceDate":"2025-04-15T03:52:06Z","VehicleInfo":[{"CompletedDataCollectTime":"2025-04-15T03:52:06Z","PlusBInformation":{"InformationDatetime":"2025-04-15T03:52:06Z","Data":[{"ValidStatus":1,"CanId":"04D8","SubId":"00","Code":"Smaph_SOC","Value":"80"},{"ValidStatus":1,"CanId":"04D8","SubId":"00","Code":"Smaph_RemDrvDist_Unit","Value":"1"},{"ValidStatus":1,"CanId":"04D8","SubId":"00","Code":"Smaph_RemDrvDist_Num","Value":"120"},{"ValidStatus":1,"CanId":"04D8","SubId":"00","Code":"Smaph_RemDrvDistEV_Num","Value":"100"},{"ValidStatus":1,"CanId":"0471","SubId":"FF","Code":"Charge_Status_sub","Value":"2"},{"ValidStatus":1,"CanId":"0422","SubId":"00","Code":"Max_Charge_Minute_QBC","Value":"12"},{"ValidStatus":1,"CanId":"0422","SubId":"00","Code":"Max_Charge_Minute_AC","Value":"12"},{"ValidStatus":1,"CanId":"0422","SubId":"00","Code":"ACChargeStatus","Value":"0"},{"ValidStatus":1,"CanId":"0422","SubId":"00","Code":"DCChargeStatus","Value":"0"},{"ValidStatus":1,"CanId":"048B","SubId":"FF","Code":"CstmzStat_BatHeat_AutoSW","Value":"1"},{"ValidStatus":1,"CanId":"0422","SubId":"01","Code":"BatteryHeater_ON","Value":"1"},{"ValidStatus":1,"CanId":"0422","SubId":"01","Code":"BatteryHeater_info1","Value":"0"},{"ValidStatus":1,"CanId":"0422","SubId":"01","Code":"BatteryHeater_info2","Value":"0"},{"ValidStatus":1,"CanId":"0422","SubId":"01","Code":"BatteryHeater_info3","Value":"0"},{"ValidStatus":1,"CanId":"0422","SubId":"01","Code":"Charger_Connector_Fitting","Value":"1"},{"ValidStatus":1,"CanId":"03F8","SubId":"FF","Code":"HP_State_PreAC_Operation","Value":"0"},{"ValidStatus":1,"CanId":"0436","SubId":"FF","Code":"atcADmodedisp","Value":"0"},{"ValidStatus":1,"CanId":"03C0","SubId":"FF","Code":"RearDef_B_Rq","Value":"0"},{"ValidStatus":1,"CanId":"03C1","SubId":"FF","Code":"InCarTe_D_Actl","Value":"580"},{"ValidStatus":1,"CanId":"03C1","SubId":"FF","Code":"InCarTe_D_Qf","Value":"3"},{"ValidStatus":1,"CanId":"0472","SubId":"FF","Code":"PreAirCon_Timer_Reservation_FS","Value":"0"}]},"IgInformation":{"InformationDatetime":"2025-04-15T03:51:10Z","Data":[{"ValidStatus":1,"CanId":"0422","SubId":"00","Code":"Battery_Deterioration","Value":"0"},{"ValidStatus":1,"CanId":"0470","SubId":"FF","Code":"SetTime_Charge","Value":"1048575"},{"ValidStatus":1,"CanId":"0470","SubId":"FF","Code":"ResetTime_Charge","Value":"1048575"}]},"PreAirConditionInformation":{"InformationDatetime":"0000-00-00T00:00:00Z"}}]}} optional={"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"internalVin":1,"vin":"JMZDR1WBJ00222009","region":"MME","blankVin":"JMZDR1WBJ00222009","vehicleUser":[{"firstName":"UserBD","lastName":"TestBD","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":1,"locale":"en-GB","internalUserId":54,"option":"{}"},{"firstName":"UserBF","lastName":"TestBF","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":0,"locale":"en-GB","internalUserId":56,"option":"{}"},{"firstName":"UserBI","lastName":"TestBI","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":1,"locale":"en-GB","internalUserId":59,"option":"{}"},{"firstName":"UserBK","lastName":"TestBK","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":1,"locale":"en-GB","internalUserId":61,"option":"{}"},{"firstName":"UserBP","lastName":"TestBP","country":"FR","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":1,"locale":"en-FR","internalUserId":66,"option":"{}"}]}}
2025-04-15 03:54:45,676262000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][LatestVehiclePersistent#1001][0] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] LatestVehiclePersistent str
2025-04-15 03:54:45,680223000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageFlow#1001][Functions#1002:Functions#1001:5222:26:14] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] execute str
2025-04-15 03:54:45,682347000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageStatResolver#1001][Functions#1002:MessageFlow#1001:5224:28:2] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] execute str
2025-04-15 03:54:45,682483000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][LatestVehiclePersistent#1001][0] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] LatestVehiclePersistent str
2025-04-15 03:54:45,833821000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1001][Functions#1002:MessageStatResolver#1001:5375:179:151] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db connection create str
2025-04-15 03:54:45,866322000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1003][Functions#1002:ConnectionWithSqlDb#1001:5408:212:33] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db token create str
2025-04-15 03:54:46,011303000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-15 03:54:48,687950000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	26	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-15 03:54:48,688601000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1004][Functions#1002:ConnectionWithSqlDb#1003:8230:3034:2822] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db token create fin
2025-04-15 03:54:48,856674000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1002][Functions#1002:ConnectionWithSqlDb#1004:8398:3202:168] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db connection create fin
2025-04-15 03:54:48,860126000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageStatResolver#1001][Functions#1002:ConnectionWithSqlDb#1002:8401:3205:3] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] doFetch_fetch str
2025-04-15 03:54:48,882320000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:3200:3200:3200] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] fetch-query str
2025-04-15 03:54:48,902471000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:3220:3220:20] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] fetch-query fin
2025-04-15 03:54:48,915509000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageStatResolver#1001][Functions#1002:MessageStatResolver#1001:8457:3261:56] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] doFetch_fetch fin
2025-04-15 03:54:48,916907000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageStatResolver#1001][Functions#1002:MessageStatResolver#1001:8458:3262:1] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] receive notice toNotRetry
2025-04-15 03:54:48,917013000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageStatResolver#1001][Functions#1002:MessageStatResolver#1001:8458:3262:0] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] execute fin
2025-04-15 03:54:48,922314000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][BizLogicLauncher#1001][Functions#1002:MessageStatResolver#1001:8464:3268:6] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] mazda.tk2.ap.online.generated.imp._170_2_2.Biz_170_2_2 str
2025-04-15 03:54:48,953724000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][BizLogicLauncher#1001][Functions#1002:BizLogicLauncher#1001:8495:3299:31] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] mazda.tk2.ap.online.generated.imp._170_2_2.Biz_170_2_2 fin
2025-04-15 03:54:48,954324000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1001][Functions#1002:BizLogicLauncher#1001:8496:3300:1] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db connection create str
2025-04-15 03:54:48,954469000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1003][Functions#1002:ConnectionWithSqlDb#1001:8496:3300:0] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db token create str
2025-04-15 03:54:48,957445000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-15 03:54:49,703617000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	35	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-15 03:54:49,704362000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1004][Functions#1002:ConnectionWithSqlDb#1003:9246:4050:750] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db token create fin
2025-04-15 03:54:49,753336000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1002][Functions#1002:ConnectionWithSqlDb#1004:9295:4099:49] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db connection create fin
2025-04-15 03:54:49,765171000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageFlow#1001][Functions#1002:ConnectionWithSqlDb#1002:9307:4111:12] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] doDetach str
2025-04-15 03:54:49,765288000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageFlow#1001][Functions#1002:MessageFlow#1001:9307:4111:0] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] doDetach_update str
2025-04-15 03:54:49,765520000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:4083:4083:863] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] checkConflict-query str
2025-04-15 03:54:49,767536000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:4085:4085:2] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] checkConflict-query fin
2025-04-15 03:54:49,772925000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:4090:4090:5] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] updateAll-query str
2025-04-15 03:54:49,776139000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:4094:4094:4] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] updateAll-query fin
2025-04-15 03:54:49,776252000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageFlow#1001][Functions#1002:MessageFlow#1001:9318:4122:11] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] doDetach_update fin
2025-04-15 03:54:49,776336000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageFlow#1001][Functions#1002:MessageFlow#1001:9318:4122:0] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] doDetach fin
2025-04-15 03:54:49,832984000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageSender#1001][Functions#1002:MessageFlow#1001:9374:4178:56] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] execute_DataSender str
2025-04-15 03:54:49,833296000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageSender#1001][Functions#1002:MessageSender#1001:9375:4179:1] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] execute_DataSender fin
2025-04-15 03:54:49,833414000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageSender#1001][Functions#1002:MessageSender#1001:9375:4179:0] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] execute_DataProviding str
2025-04-15 03:54:50,193844000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageSender#1001][Functions#1002:MessageSender#1001:9735:4539:360] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] execute_DataProviding fin
2025-04-15 03:54:50,194092000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageFlow#1001][Functions#1002:MessageSender#1001:9736:4540:1] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] doCreateSchedule str
2025-04-15 03:54:50,194510000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1001][Functions#1002:MessageFlow#1001:9736:4540:0] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db connection create str
2025-04-15 03:54:50,194685000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1003][Functions#1002:ConnectionWithSqlDb#1001:9736:4540:0] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db token create str
2025-04-15 03:54:50,197941000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-15 03:54:50,918461000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	42	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-15 03:54:50,919353000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1004][Functions#1002:ConnectionWithSqlDb#1003:10460:5264:724] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db token create fin
2025-04-15 03:54:50,977561000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][ConnectionWithSqlDb#1002][Functions#1002:ConnectionWithSqlDb#1004:10519:5323:59] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] db connection create fin
2025-04-15 03:54:50,979597000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageFlow#1001][Functions#1002:ConnectionWithSqlDb#1002:10521:5325:2] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] doEntrySchedule_insert str
2025-04-15 03:54:50,985852000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageFlow#1001][Functions#1002:MessageFlow#1001:10527:5331:6] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] doEntrySchedule_insert fin
2025-04-15 03:54:50,998391000	I	apf-msg-idle-01-cf7b4ddcb-7z564	apf-msg-flow	62	16	[0effae58-f4c6-4172-9024-8fdbda31e975][MessageFlow#1001][Functions#1002:MessageFlow#1001:10540:5344:13] [170_2_2:20:* apf-msg-idle-01-cf7b4ddcb-7z564] execute fin
