2025-04-15 02:43:23,784966000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][Functions#1002][3123] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] run str data={"Vin":"JMZDR1WBJ00222009","FileName":"250415_024306_08.alt","ReceiveDateTime":"2025-04-15T02:43:12Z","FileData":"209999999999900000FFFFFFFFFFFF07E90004000F00020024002204040081E01FA51E28A1010F1BDF3A3A6000C0000F0000000000019507E90004000F0002002B000600020001"} optional={"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"internalVin":1,"vin":"JMZDR1WBJ00222009","region":"MME","blankVin":"JMZDR1WBJ00222009","vehicleUser":[{"firstName":"UserBD","lastName":"TestBD","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":1,"locale":"en-GB","internalUserId":54,"option":"{}"},{"firstName":"UserBF","lastName":"TestBF","country":"DE","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":1,"locale":"en-DE","internalUserId":56,"option":"{}"},{"firstName":"UserBI","lastName":"TestBI","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":1,"locale":"en-GB","internalUserId":59,"option":"{}"},{"firstName":"UserBK","lastName":"TestBK","country":"GB","formatInfo":"10000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":0,"locale":"en-GB","internalUserId":61,"option":"{}"},{"firstName":"UserBP","lastName":"TestBP","country":"FR","formatInfo":"00000000","mailAddress":"<EMAIL>","tel":"+819036544737","middleName":"User","userType":1,"locale":"en-FR","internalUserId":66,"option":"{}"}]}}
2025-04-15 02:43:23,800116000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][LatestVehiclePersistent#1001][0] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] LatestVehiclePersistent str
2025-04-15 02:43:23,802746000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageFlow#1001][Functions#1002:Functions#1001:3148:25:16] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] execute str
2025-04-15 02:43:23,804358000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageStatResolver#1001][Functions#1002:MessageFlow#1001:3150:27:2] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] execute str
2025-04-15 02:43:23,804477000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][LatestVehiclePersistent#1001][0] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] LatestVehiclePersistent str
2025-04-15 02:43:23,931336000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][ConnectionWithSqlDb#1001][Functions#1002:MessageStatResolver#1001:3277:154:127] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] db connection create str
2025-04-15 02:43:23,967267000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][ConnectionWithSqlDb#1003][Functions#1002:ConnectionWithSqlDb#1001:3313:190:36] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] db token create str
2025-04-15 02:43:24,084863000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-15 02:43:25,559691000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	26	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-15 02:43:25,560358000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][ConnectionWithSqlDb#1004][Functions#1002:ConnectionWithSqlDb#1003:4906:1783:1593] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] db token create fin
2025-04-15 02:43:25,708703000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][ConnectionWithSqlDb#1002][Functions#1002:ConnectionWithSqlDb#1004:5054:1931:148] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] db connection create fin
2025-04-15 02:43:25,711132000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageStatResolver#1001][Functions#1002:ConnectionWithSqlDb#1002:5057:1934:3] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] doFetch_fetch str
2025-04-15 02:43:25,734516000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:1930:1930:1930] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] fetch-query str
2025-04-15 02:43:25,755488000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:1951:1951:21] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] fetch-query fin
2025-04-15 02:43:25,767994000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageStatResolver#1001][Functions#1002:MessageStatResolver#1001:5113:1990:56] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] doFetch_fetch fin
2025-04-15 02:43:25,769189000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageStatResolver#1001][Functions#1002:MessageStatResolver#1001:5115:1992:2] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] receive notice toNotRetry
2025-04-15 02:43:25,769309000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageStatResolver#1001][Functions#1002:MessageStatResolver#1001:5115:1992:0] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] execute fin
2025-04-15 02:43:25,774726000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][BizLogicLauncher#1001][Functions#1002:MessageStatResolver#1001:5120:1997:5] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] mazda.tk2.ap.online.generated.imp._100_2_1.Biz_100_2_1 str
2025-04-15 02:43:25,785957000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][BizLogicLauncher#1001][Functions#1002:BizLogicLauncher#1001:5131:2008:11] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] mazda.tk2.ap.online.generated.imp._100_2_1.Biz_100_2_1 fin
2025-04-15 02:43:25,786666000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][ConnectionWithSqlDb#1001][Functions#1002:BizLogicLauncher#1001:5132:2009:1] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] db connection create str
2025-04-15 02:43:25,786842000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][ConnectionWithSqlDb#1003][Functions#1002:ConnectionWithSqlDb#1001:5132:2009:0] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] db token create str
2025-04-15 02:43:25,789707000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	Azure Identity => Attempted credential EnvironmentCredential is unavailable.
2025-04-15 02:43:26,592590000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	35	Azure Identity => Attempted credential WorkloadIdentityCredential returns a token
2025-04-15 02:43:26,593242000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][ConnectionWithSqlDb#1004][Functions#1002:ConnectionWithSqlDb#1003:5938:2815:806] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] db token create fin
2025-04-15 02:43:26,642694000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][ConnectionWithSqlDb#1002][Functions#1002:ConnectionWithSqlDb#1004:5988:2865:50] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] db connection create fin
2025-04-15 02:43:26,658528000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageFlow#1001][Functions#1002:ConnectionWithSqlDb#1002:6004:2881:16] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] doDetach str
2025-04-15 02:43:26,658667000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageFlow#1001][Functions#1002:MessageFlow#1001:6004:2881:0] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] doDetach_update str
2025-04-15 02:43:26,658879000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:2854:2854:903] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] checkConflict-query str
2025-04-15 02:43:26,660256000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:2856:2856:2] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] checkConflict-query fin
2025-04-15 02:43:26,666269000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:2862:2862:6] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] updateAll-query str
2025-04-15 02:43:26,669452000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][LatestVehiclePersistent#1001][LatestVehiclePersistent#1001:LatestVehiclePersistent#1001:2865:2865:3] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] updateAll-query fin
2025-04-15 02:43:26,669770000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageFlow#1001][Functions#1002:MessageFlow#1001:6015:2892:11] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] doDetach_update fin
2025-04-15 02:43:26,669852000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageFlow#1001][Functions#1002:MessageFlow#1001:6015:2892:0] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] doDetach fin
2025-04-15 02:43:26,677294000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageSender#1001][Functions#1002:MessageFlow#1001:6023:2900:8] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] execute_DataSender str
2025-04-15 02:43:26,677539000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageSender#1001][Functions#1002:MessageSender#1001:6023:2900:0] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] execute_DataSender fin
2025-04-15 02:43:26,677624000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageSender#1001][Functions#1002:MessageSender#1001:6023:2900:0] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] execute_DataProviding str
2025-04-15 02:43:27,027521000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageSender#1001][Functions#1002:MessageSender#1001:6373:3250:350] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] execute_DataProviding fin
2025-04-15 02:43:27,027724000	I	apf-msg-idle-01-cf7b4ddcb-4q2cd	apf-msg-flow	61	16	[a827683e-ee51-432a-a1e7-960649a562b1][MessageFlow#1001][Functions#1002:MessageSender#1001:6373:3250:0] [100_2_1:20:* apf-msg-idle-01-cf7b4ddcb-4q2cd] execute fin
