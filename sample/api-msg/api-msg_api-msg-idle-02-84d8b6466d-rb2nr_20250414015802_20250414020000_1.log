2025-04-14 01:02:02,885884000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	140	[ffdaf4bc-163a-4379-ae36-654fc428e2fb][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:02:02,886372000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	140	[ffdaf4bc-163a-4379-ae36-654fc428e2fb][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:02:02,886883000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	140	[ffdaf4bc-163a-4379-ae36-654fc428e2fb][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:02:03,575956000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	140	[ffdaf4bc-163a-4379-ae36-654fc428e2fb][Communicator#1003][Communicator#1000:Communicator#1002:690:690:689] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:02:03,576326000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	140	[ffdaf4bc-163a-4379-ae36-654fc428e2fb][Communicator#1023][Communicator#1000:Communicator#1003:691:691:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:02:03,576785000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	140	[ffdaf4bc-163a-4379-ae36-654fc428e2fb][Communicator#1006][Communicator#1000:Communicator#1023:691:691:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:16:01,928637000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	145	[c40c801e-c063-475c-a4a3-974045247caf][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:16:01,929002000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	145	[c40c801e-c063-475c-a4a3-974045247caf][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:16:01,929497000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	145	[c40c801e-c063-475c-a4a3-974045247caf][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:16:02,465114000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	145	[c40c801e-c063-475c-a4a3-974045247caf][Communicator#1003][Communicator#1000:Communicator#1002:537:537:536] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:16:02,465403000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	145	[c40c801e-c063-475c-a4a3-974045247caf][Communicator#1023][Communicator#1000:Communicator#1003:537:537:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:16:02,465776000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	145	[c40c801e-c063-475c-a4a3-974045247caf][Communicator#1006][Communicator#1000:Communicator#1023:537:537:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:18:02,219176000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[4097e02e-af77-4862-93d9-2b46835c478b][Communicator#1000][1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:18:02,219555000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[4097e02e-af77-4862-93d9-2b46835c478b][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:18:02,220012000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[4097e02e-af77-4862-93d9-2b46835c478b][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:18:02,800128000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[4097e02e-af77-4862-93d9-2b46835c478b][Communicator#1003][Communicator#1000:Communicator#1002:582:581:581] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:18:02,800400000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[4097e02e-af77-4862-93d9-2b46835c478b][Communicator#1023][Communicator#1000:Communicator#1003:582:581:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:18:02,800765000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[4097e02e-af77-4862-93d9-2b46835c478b][Communicator#1006][Communicator#1000:Communicator#1023:582:581:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:19:02,307323000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[48760b32-bf0d-4148-bb72-5ebacdcec1d5][Communicator#1000][1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:19:02,307531000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[48760b32-bf0d-4148-bb72-5ebacdcec1d5][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:19:02,307991000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[48760b32-bf0d-4148-bb72-5ebacdcec1d5][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:19:03,657457000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[48760b32-bf0d-4148-bb72-5ebacdcec1d5][Communicator#1003][Communicator#1000:Communicator#1002:1351:1350:1350] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:19:03,657842000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[48760b32-bf0d-4148-bb72-5ebacdcec1d5][Communicator#1023][Communicator#1000:Communicator#1003:1351:1350:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:19:03,658215000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	150	[48760b32-bf0d-4148-bb72-5ebacdcec1d5][Communicator#1006][Communicator#1000:Communicator#1023:1352:1351:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:22:02,269710000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	158	[969cab98-2cee-4b41-96c2-ce9515daaab1][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:22:02,270047000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	158	[969cab98-2cee-4b41-96c2-ce9515daaab1][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:22:02,270587000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	158	[969cab98-2cee-4b41-96c2-ce9515daaab1][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:22:02,667987000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	158	[969cab98-2cee-4b41-96c2-ce9515daaab1][Communicator#1003][Communicator#1000:Communicator#1002:398:398:397] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:22:02,668271000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	158	[969cab98-2cee-4b41-96c2-ce9515daaab1][Communicator#1023][Communicator#1000:Communicator#1003:399:399:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:22:02,668678000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	158	[969cab98-2cee-4b41-96c2-ce9515daaab1][Communicator#1006][Communicator#1000:Communicator#1023:399:399:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:31:02,177193000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	163	[6480d00b-7056-495f-94c1-0128b24d62a6][Communicator#1000][1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:31:02,177533000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	163	[6480d00b-7056-495f-94c1-0128b24d62a6][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:31:02,178008000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	163	[6480d00b-7056-495f-94c1-0128b24d62a6][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:31:02,205271000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	163	[6480d00b-7056-495f-94c1-0128b24d62a6][Communicator#1003][Communicator#1000:Communicator#1002:29:28:28] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:31:02,205572000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	163	[6480d00b-7056-495f-94c1-0128b24d62a6][Communicator#1023][Communicator#1000:Communicator#1003:29:28:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:31:02,205981000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	163	[6480d00b-7056-495f-94c1-0128b24d62a6][Communicator#1006][Communicator#1000:Communicator#1023:29:28:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:40:03,562169000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	168	[07bdd361-d3eb-4eb0-a9cf-5e3c214ef7c6][Communicator#1000][1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:40:03,562507000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	168	[07bdd361-d3eb-4eb0-a9cf-5e3c214ef7c6][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:40:03,562935000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	168	[07bdd361-d3eb-4eb0-a9cf-5e3c214ef7c6][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:40:03,588512000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	168	[07bdd361-d3eb-4eb0-a9cf-5e3c214ef7c6][Communicator#1003][Communicator#1000:Communicator#1002:27:26:26] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:40:03,588731000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	168	[07bdd361-d3eb-4eb0-a9cf-5e3c214ef7c6][Communicator#1023][Communicator#1000:Communicator#1003:27:26:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:40:03,589038000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	168	[07bdd361-d3eb-4eb0-a9cf-5e3c214ef7c6][Communicator#1006][Communicator#1000:Communicator#1023:28:27:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:42:02,512263000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[9675e428-4306-4105-b938-c559cc5b7f11][Communicator#1000][1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:42:02,512593000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[9675e428-4306-4105-b938-c559cc5b7f11][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:42:02,513011000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[9675e428-4306-4105-b938-c559cc5b7f11][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:42:02,512959000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	174	[5f4d87c3-bcde-494f-bc30-9ed5bcfc7c18][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:42:02,513536000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	174	[5f4d87c3-bcde-494f-bc30-9ed5bcfc7c18][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:42:02,513954000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	174	[5f4d87c3-bcde-494f-bc30-9ed5bcfc7c18][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:42:03,596198000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	174	[5f4d87c3-bcde-494f-bc30-9ed5bcfc7c18][Communicator#1003][Communicator#1000:Communicator#1002:1084:1084:1083] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:42:03,596492000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	174	[5f4d87c3-bcde-494f-bc30-9ed5bcfc7c18][Communicator#1023][Communicator#1000:Communicator#1003:1084:1084:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:42:03,596914000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	174	[5f4d87c3-bcde-494f-bc30-9ed5bcfc7c18][Communicator#1006][Communicator#1000:Communicator#1023:1084:1084:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:42:03,622841000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[9675e428-4306-4105-b938-c559cc5b7f11][Communicator#1003][Communicator#1000:Communicator#1002:1111:1110:1110] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:42:03,623070000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[9675e428-4306-4105-b938-c559cc5b7f11][Communicator#1023][Communicator#1000:Communicator#1003:1112:1111:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:42:03,623430000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[9675e428-4306-4105-b938-c559cc5b7f11][Communicator#1006][Communicator#1000:Communicator#1023:1112:1111:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:43:02,451470000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[f6828955-c1ca-435c-9f59-4164916458c3][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:43:02,451662000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[f6828955-c1ca-435c-9f59-4164916458c3][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:43:02,452021000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[f6828955-c1ca-435c-9f59-4164916458c3][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:43:03,518576000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[f6828955-c1ca-435c-9f59-4164916458c3][Communicator#1003][Communicator#1000:Communicator#1002:1067:1067:1067] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:43:03,518902000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[f6828955-c1ca-435c-9f59-4164916458c3][Communicator#1023][Communicator#1000:Communicator#1003:1067:1067:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:43:03,519212000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	173	[f6828955-c1ca-435c-9f59-4164916458c3][Communicator#1006][Communicator#1000:Communicator#1023:1068:1068:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:58:02,354251000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[ee9f0880-3243-4017-b3d6-03edd2f5ca1b][Communicator#1000][1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:58:02,354581000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[ee9f0880-3243-4017-b3d6-03edd2f5ca1b][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:58:02,355005000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[ee9f0880-3243-4017-b3d6-03edd2f5ca1b][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:58:02,374922000	E	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[ee9f0880-3243-4017-b3d6-03edd2f5ca1b][Communicator#1022][Communicator#1000:Communicator#1002:21:20:20] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Access Error fin
2025-04-14 01:58:02,375981000	F	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[ee9f0880-3243-4017-b3d6-03edd2f5ca1b][Communicator#999][Communicator#1000:Communicator#1022:22:21:1] java.util.concurrent.CompletionException: java.net.ConnectException: Connection refused
	java.base/java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:367)
	java.base/java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:376)
	java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1074)
	java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:506)
	java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2088)
	java.net.http/jdk.internal.net.http.PlainHttpConnection$ConnectEvent.lambda$handle$1(PlainHttpConnection.java:137)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-14 01:58:02,376417000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[ee9f0880-3243-4017-b3d6-03edd2f5ca1b][Communicator#1006][Communicator#1000:Communicator#?:23:22:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
2025-04-14 01:58:02,443391000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[352bddfa-e68a-4076-bfa3-e2d5aa56379a][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] TscCommunicator str
2025-04-14 01:58:02,443548000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[352bddfa-e68a-4076-bfa3-e2d5aa56379a][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest str
2025-04-14 01:58:02,443900000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[352bddfa-e68a-4076-bfa3-e2d5aa56379a][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] authenticate fin
2025-04-14 01:58:02,459492000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[352bddfa-e68a-4076-bfa3-e2d5aa56379a][Communicator#1003][Communicator#1000:Communicator#1002:16:16:16] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] accessCustomerClient fin
2025-04-14 01:58:02,459683000	W	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[352bddfa-e68a-4076-bfa3-e2d5aa56379a][Communicator#1023][Communicator#1000:Communicator#1003:16:16:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] CustomerClient Data Error fin
2025-04-14 01:58:02,459888000	I	api-msg-idle-02-84d8b6466d-rb2nr	api-msg	24	182	[352bddfa-e68a-4076-bfa3-e2d5aa56379a][Communicator#1006][Communicator#1000:Communicator#1023:16:16:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-rb2nr] invokeRequest fin
