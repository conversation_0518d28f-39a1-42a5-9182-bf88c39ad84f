2025-04-16 08:00:00,002457000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	530	[d82c1eb0-7398-4337-92a9-ca2ee3f393de][Communicator#1000][0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] UpdateKeyCommunicator str
2025-04-16 08:00:00,002722000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	530	[d82c1eb0-7398-4337-92a9-ca2ee3f393de][SecretStore#120][Communicator#1000:Communicator#1000:0:0:0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] update key str
2025-04-16 08:00:00,002772000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	530	[d82c1eb0-7398-4337-92a9-ca2ee3f393de][SecretStore#204][Communicator#1000:SecretStore#120:0:0:0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] get key list from cloud store
2025-04-16 08:00:00,002795000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	530	[d82c1eb0-7398-4337-92a9-ca2ee3f393de][SecretStore#207][Communicator#1000:SecretStore#204:0:0:0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] access to cloud store
2025-04-16 08:00:00,515338000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	530	[d82c1eb0-7398-4337-92a9-ca2ee3f393de][SecretStore#121][Communicator#1000:SecretStore#207:513:513:513] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] key is already updated
2025-04-16 08:00:00,515402000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	530	[d82c1eb0-7398-4337-92a9-ca2ee3f393de][SecretStore#122][Communicator#1000:SecretStore#121:513:513:0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] update key fin
2025-04-16 08:00:00,515429000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	530	[d82c1eb0-7398-4337-92a9-ca2ee3f393de][Communicator#1007][Communicator#1000:SecretStore#122:513:513:0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] execute fin
2025-04-16 08:05:02,440506000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	536	[a886abf1-e2d3-4710-b14a-76ccd8a04b96][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] TscCommunicator str
2025-04-16 08:05:02,440712000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	536	[a886abf1-e2d3-4710-b14a-76ccd8a04b96][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest str
2025-04-16 08:05:02,440967000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	536	[a886abf1-e2d3-4710-b14a-76ccd8a04b96][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] authenticate fin
2025-04-16 08:05:02,464746000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	536	[a886abf1-e2d3-4710-b14a-76ccd8a04b96][Communicator#1003][Communicator#1000:Communicator#1002:24:24:24] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] accessCustomerClient fin
2025-04-16 08:05:02,464883000	W	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	536	[a886abf1-e2d3-4710-b14a-76ccd8a04b96][Communicator#1023][Communicator#1000:Communicator#1003:24:24:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] CustomerClient Data Error fin
2025-04-16 08:05:02,465100000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	536	[a886abf1-e2d3-4710-b14a-76ccd8a04b96][Communicator#1006][Communicator#1000:Communicator#1023:25:25:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest fin
2025-04-16 08:18:02,077526000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	541	[036621a7-966b-4ecd-a3ed-bf6d17d0b954][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] TscCommunicator str
2025-04-16 08:18:02,077695000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	541	[036621a7-966b-4ecd-a3ed-bf6d17d0b954][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest str
2025-04-16 08:18:02,077945000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	541	[036621a7-966b-4ecd-a3ed-bf6d17d0b954][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] authenticate fin
2025-04-16 08:18:02,100939000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	541	[036621a7-966b-4ecd-a3ed-bf6d17d0b954][Communicator#1003][Communicator#1000:Communicator#1002:23:23:23] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] accessCustomerClient fin
2025-04-16 08:18:02,101051000	W	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	541	[036621a7-966b-4ecd-a3ed-bf6d17d0b954][Communicator#1023][Communicator#1000:Communicator#1003:24:24:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] CustomerClient Data Error fin
2025-04-16 08:18:02,101251000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	541	[036621a7-966b-4ecd-a3ed-bf6d17d0b954][Communicator#1006][Communicator#1000:Communicator#1023:24:24:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest fin
2025-04-16 08:30:00,003554000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	546	[ec5d4ec1-871e-4a88-8b9e-ef15e7b5c2d6][Communicator#1000][0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] SaveKeyCommunicator str
2025-04-16 08:30:00,003747000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	546	[ec5d4ec1-871e-4a88-8b9e-ef15e7b5c2d6][SecretStore#110][Communicator#1000:Communicator#1000:0:0:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] save next key str
2025-04-16 08:30:00,003846000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	546	[ec5d4ec1-871e-4a88-8b9e-ef15e7b5c2d6][SecretStore#112][Communicator#1000:SecretStore#110:0:0:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] next key is already exists
2025-04-16 08:30:00,003919000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	546	[ec5d4ec1-871e-4a88-8b9e-ef15e7b5c2d6][SecretStore#113][Communicator#1000:SecretStore#112:0:0:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] save next key fin
2025-04-16 08:30:00,003947000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	546	[ec5d4ec1-871e-4a88-8b9e-ef15e7b5c2d6][Communicator#1007][Communicator#1000:SecretStore#113:0:0:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-4p4f2] execute fin
2025-04-16 08:34:01,912432000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[29911d80-b189-4e76-8327-219831572d57][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] TscCommunicator str
2025-04-16 08:34:01,912588000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[29911d80-b189-4e76-8327-219831572d57][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest str
2025-04-16 08:34:01,912818000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[29911d80-b189-4e76-8327-219831572d57][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] authenticate fin
2025-04-16 08:34:01,938207000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[29911d80-b189-4e76-8327-219831572d57][Communicator#1003][Communicator#1000:Communicator#1002:26:26:26] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] accessCustomerClient fin
2025-04-16 08:34:01,938307000	W	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[29911d80-b189-4e76-8327-219831572d57][Communicator#1023][Communicator#1000:Communicator#1003:26:26:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] CustomerClient Data Error fin
2025-04-16 08:34:01,938501000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[29911d80-b189-4e76-8327-219831572d57][Communicator#1006][Communicator#1000:Communicator#1023:26:26:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest fin
2025-04-16 08:34:02,431319000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[503b8f8e-161d-4f4a-b083-d61229b93441][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] TscCommunicator str
2025-04-16 08:34:02,431410000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[503b8f8e-161d-4f4a-b083-d61229b93441][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest str
2025-04-16 08:34:02,431667000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[503b8f8e-161d-4f4a-b083-d61229b93441][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] authenticate fin
2025-04-16 08:34:02,443363000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[503b8f8e-161d-4f4a-b083-d61229b93441][Communicator#1003][Communicator#1000:Communicator#1002:12:12:12] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] accessCustomerClient fin
2025-04-16 08:34:02,443465000	W	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[503b8f8e-161d-4f4a-b083-d61229b93441][Communicator#1023][Communicator#1000:Communicator#1003:12:12:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] CustomerClient Data Error fin
2025-04-16 08:34:02,443611000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	548	[503b8f8e-161d-4f4a-b083-d61229b93441][Communicator#1006][Communicator#1000:Communicator#1023:12:12:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest fin
2025-04-16 08:47:02,277850000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	555	[a842fff5-55aa-446c-a4dc-75af5165b637][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] TscCommunicator str
2025-04-16 08:47:02,277990000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	555	[a842fff5-55aa-446c-a4dc-75af5165b637][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest str
2025-04-16 08:47:02,278196000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	555	[a842fff5-55aa-446c-a4dc-75af5165b637][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] authenticate fin
2025-04-16 08:47:02,301920000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	555	[a842fff5-55aa-446c-a4dc-75af5165b637][Communicator#1003][Communicator#1000:Communicator#1002:24:24:23] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] accessCustomerClient fin
2025-04-16 08:47:02,302036000	W	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	555	[a842fff5-55aa-446c-a4dc-75af5165b637][Communicator#1023][Communicator#1000:Communicator#1003:25:25:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] CustomerClient Data Error fin
2025-04-16 08:47:02,302279000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	555	[a842fff5-55aa-446c-a4dc-75af5165b637][Communicator#1006][Communicator#1000:Communicator#1023:25:25:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest fin
2025-04-16 08:49:02,145558000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[4a87717e-a077-46c3-9998-77859e5cb105][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] TscCommunicator str
2025-04-16 08:49:02,145807000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[4a87717e-a077-46c3-9998-77859e5cb105][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest str
2025-04-16 08:49:02,146069000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[4a87717e-a077-46c3-9998-77859e5cb105][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] authenticate fin
2025-04-16 08:49:02,170922000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[4a87717e-a077-46c3-9998-77859e5cb105][Communicator#1003][Communicator#1000:Communicator#1002:25:25:24] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] accessCustomerClient fin
2025-04-16 08:49:02,171035000	W	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[4a87717e-a077-46c3-9998-77859e5cb105][Communicator#1023][Communicator#1000:Communicator#1003:26:26:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] CustomerClient Data Error fin
2025-04-16 08:49:02,171288000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[4a87717e-a077-46c3-9998-77859e5cb105][Communicator#1006][Communicator#1000:Communicator#1023:26:26:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest fin
2025-04-16 08:49:42,556879000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Communicator#1000][0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] AppCommunicator str
2025-04-16 08:49:42,558036000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Communicator#1001][Communicator#1000:Communicator#1000:2:2:2] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest str
2025-04-16 08:49:42,558618000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#100][Communicator#1000:Communicator#1001:2:2:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key str
2025-04-16 08:49:42,558869000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#101][Communicator#1000:SecretStore#100:2:2:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key list from local file
2025-04-16 08:49:42,559025000	W	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#201][Communicator#1000:SecretStore#101:3:3:1] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] no local file for today
2025-04-16 08:49:42,559082000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#202][Communicator#1000:SecretStore#201:3:3:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key list from store
2025-04-16 08:49:42,559106000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#204][Communicator#1000:SecretStore#202:3:3:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key list from cloud store
2025-04-16 08:49:42,559126000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#207][Communicator#1000:SecretStore#204:3:3:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] access to cloud store
2025-04-16 08:49:43,110524000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#208][Communicator#1000:SecretStore#207:554:554:551] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] save first key to local file
2025-04-16 08:49:43,110828000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#210][Communicator#1000:SecretStore#208:554:554:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] save key to local file success
2025-04-16 08:49:43,110865000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#209][Communicator#1000:SecretStore#210:554:554:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] save second key to local file
2025-04-16 08:49:43,111269000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#210][Communicator#1000:SecretStore#209:555:555:1] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] save key to local file success
2025-04-16 08:49:43,111436000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#102][Communicator#1000:SecretStore#210:555:555:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key fin
2025-04-16 08:49:44,059134000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Communicator#1002][Communicator#1000:SecretStore#102:1503:1503:948] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] authenticate fin
2025-04-16 08:49:44,063566000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#100][Communicator#1000:Communicator#1002:1507:1507:4] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key str
2025-04-16 08:49:44,063648000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#101][Communicator#1000:SecretStore#100:1507:1507:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key list from local file
2025-04-16 08:49:44,063792000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#102][Communicator#1000:SecretStore#101:1507:1507:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key fin
2025-04-16 08:49:44,142983000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Communicator#1003][Communicator#1000:SecretStore#102:1586:1586:79] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] accessCustomerClient fin
2025-04-16 08:49:49,824605000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Communicator#1004][Communicator#1000:Communicator#1003:7268:7268:5682] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] sendMessage fin
2025-04-16 08:49:49,832438000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][ConnectionWithSqlDb#1001][Communicator#1000:Communicator#1004:7276:7276:8] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] db connection create str
2025-04-16 08:49:49,847392000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][ConnectionWithSqlDb#1003][Communicator#1000:ConnectionWithSqlDb#1001:7291:7291:15] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] db token create str
2025-04-16 08:49:50,341697000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][ConnectionWithSqlDb#1004][Communicator#1000:ConnectionWithSqlDb#1003:7785:7785:494] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] db token create fin
2025-04-16 08:49:50,469378000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][ConnectionWithSqlDb#1002][Communicator#1000:ConnectionWithSqlDb#1004:7913:7913:128] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] db connection create fin
2025-04-16 08:49:50,524161000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#100][Communicator#1000:ConnectionWithSqlDb#1002:7968:7968:55] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key str
2025-04-16 08:49:50,524258000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#101][Communicator#1000:SecretStore#100:7968:7968:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key list from local file
2025-04-16 08:49:50,524400000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][SecretStore#102][Communicator#1000:SecretStore#101:7968:7968:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] get key fin
2025-04-16 08:49:50,524851000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[3f54dc05-89ed-4064-bc16-9b9c013d5860][Communicator#1006][Communicator#1000:SecretStore#102:7968:7968:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest fin
2025-04-16 08:50:02,285577000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[fdd4a104-6bab-4bcd-9d57-2b56a0d63623][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] TscCommunicator str
2025-04-16 08:50:02,285693000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[fdd4a104-6bab-4bcd-9d57-2b56a0d63623][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest str
2025-04-16 08:50:02,285942000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[fdd4a104-6bab-4bcd-9d57-2b56a0d63623][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] authenticate fin
2025-04-16 08:50:02,297087000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[fdd4a104-6bab-4bcd-9d57-2b56a0d63623][Communicator#1003][Communicator#1000:Communicator#1002:12:12:12] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] accessCustomerClient fin
2025-04-16 08:50:02,297185000	W	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[fdd4a104-6bab-4bcd-9d57-2b56a0d63623][Communicator#1023][Communicator#1000:Communicator#1003:12:12:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] CustomerClient Data Error fin
2025-04-16 08:50:02,297353000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	560	[fdd4a104-6bab-4bcd-9d57-2b56a0d63623][Communicator#1006][Communicator#1000:Communicator#1023:12:12:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest fin
2025-04-16 08:58:02,764284000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	581	[0544c742-2dcf-4f65-a591-44e5fdf68055][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] TscCommunicator str
2025-04-16 08:58:02,764494000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	581	[0544c742-2dcf-4f65-a591-44e5fdf68055][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest str
2025-04-16 08:58:02,764751000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	581	[0544c742-2dcf-4f65-a591-44e5fdf68055][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] authenticate fin
2025-04-16 08:58:02,791363000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	581	[0544c742-2dcf-4f65-a591-44e5fdf68055][Communicator#1003][Communicator#1000:Communicator#1002:27:27:27] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] accessCustomerClient fin
2025-04-16 08:58:02,791478000	W	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	581	[0544c742-2dcf-4f65-a591-44e5fdf68055][Communicator#1023][Communicator#1000:Communicator#1003:27:27:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] CustomerClient Data Error fin
2025-04-16 08:58:02,791682000	I	api-msg-idle-01-55d9b47b86-4p4f2	api-msg	24	581	[0544c742-2dcf-4f65-a591-44e5fdf68055][Communicator#1006][Communicator#1000:Communicator#1023:27:27:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-4p4f2] invokeRequest fin
