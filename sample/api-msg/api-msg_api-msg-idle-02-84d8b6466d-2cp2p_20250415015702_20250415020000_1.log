2025-04-15 01:04:02,909023000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	144	[c9ab6c30-19fc-49bb-9723-115ae4efa0ea][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:04:02,909444000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	144	[c9ab6c30-19fc-49bb-9723-115ae4efa0ea][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:04:02,909918000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	144	[c9ab6c30-19fc-49bb-9723-115ae4efa0ea][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:04:03,362739000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	144	[c9ab6c30-19fc-49bb-9723-115ae4efa0ea][Communicator#1003][Communicator#1000:Communicator#1002:454:454:453] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:04:03,363084000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	144	[c9ab6c30-19fc-49bb-9723-115ae4efa0ea][Communicator#1023][Communicator#1000:Communicator#1003:455:455:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:04:03,363577000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	144	[c9ab6c30-19fc-49bb-9723-115ae4efa0ea][Communicator#1006][Communicator#1000:Communicator#1023:455:455:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:11:02,814931000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	149	[1762263d-322e-4818-86b8-affd10361c37][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:11:02,815283000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	149	[1762263d-322e-4818-86b8-affd10361c37][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:11:02,815741000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	149	[1762263d-322e-4818-86b8-affd10361c37][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:11:02,844607000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	149	[1762263d-322e-4818-86b8-affd10361c37][Communicator#1003][Communicator#1000:Communicator#1002:30:30:29] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:11:02,844865000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	149	[1762263d-322e-4818-86b8-affd10361c37][Communicator#1023][Communicator#1000:Communicator#1003:30:30:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:11:02,845223000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	149	[1762263d-322e-4818-86b8-affd10361c37][Communicator#1006][Communicator#1000:Communicator#1023:31:31:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:14:02,425610000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	154	[b83efb98-9ab4-43a7-948c-30dd4df801ad][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:14:02,425968000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	154	[b83efb98-9ab4-43a7-948c-30dd4df801ad][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:14:02,426442000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	154	[b83efb98-9ab4-43a7-948c-30dd4df801ad][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:14:02,447494000	E	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	154	[b83efb98-9ab4-43a7-948c-30dd4df801ad][Communicator#1022][Communicator#1000:Communicator#1002:22:22:21] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Access Error fin
2025-04-15 01:14:02,448646000	F	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	154	[b83efb98-9ab4-43a7-948c-30dd4df801ad][Communicator#999][Communicator#1000:Communicator#1022:23:23:1] java.util.concurrent.CompletionException: java.net.ConnectException: Connection refused
	java.base/java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:367)
	java.base/java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:376)
	java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1074)
	java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:506)
	java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2088)
	java.net.http/jdk.internal.net.http.PlainHttpConnection$ConnectEvent.lambda$handle$1(PlainHttpConnection.java:137)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-15 01:14:02,449140000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	154	[b83efb98-9ab4-43a7-948c-30dd4df801ad][Communicator#1006][Communicator#1000:Communicator#?:24:24:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:16:01,799468000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	158	[b321a206-a72f-44c0-9701-0ac39c0c8c68][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:16:01,799878000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	158	[b321a206-a72f-44c0-9701-0ac39c0c8c68][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:16:01,800356000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	158	[b321a206-a72f-44c0-9701-0ac39c0c8c68][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:16:02,565071000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	158	[b321a206-a72f-44c0-9701-0ac39c0c8c68][Communicator#1003][Communicator#1000:Communicator#1002:765:765:764] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:16:02,565390000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	158	[b321a206-a72f-44c0-9701-0ac39c0c8c68][Communicator#1023][Communicator#1000:Communicator#1003:766:766:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:16:02,565797000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	158	[b321a206-a72f-44c0-9701-0ac39c0c8c68][Communicator#1006][Communicator#1000:Communicator#1023:766:766:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:18:02,251916000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[c556104a-ace0-4840-a430-6c3533f883d0][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:18:02,252273000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[c556104a-ace0-4840-a430-6c3533f883d0][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:18:02,252752000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[c556104a-ace0-4840-a430-6c3533f883d0][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:18:02,693518000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[c556104a-ace0-4840-a430-6c3533f883d0][Communicator#1003][Communicator#1000:Communicator#1002:442:442:441] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:18:02,693831000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[c556104a-ace0-4840-a430-6c3533f883d0][Communicator#1023][Communicator#1000:Communicator#1003:442:442:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:18:02,694248000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[c556104a-ace0-4840-a430-6c3533f883d0][Communicator#1006][Communicator#1000:Communicator#1023:443:443:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:19:02,409365000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[458bedbe-3f36-45a0-8cf5-303a285ff78d][Communicator#1000][1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:19:02,409624000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[458bedbe-3f36-45a0-8cf5-303a285ff78d][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:19:02,410094000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[458bedbe-3f36-45a0-8cf5-303a285ff78d][Communicator#1002][Communicator#1000:Communicator#1001:2:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:19:02,838103000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[458bedbe-3f36-45a0-8cf5-303a285ff78d][Communicator#1003][Communicator#1000:Communicator#1002:429:428:427] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:19:02,838438000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[458bedbe-3f36-45a0-8cf5-303a285ff78d][Communicator#1023][Communicator#1000:Communicator#1003:430:429:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:19:02,838745000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	163	[458bedbe-3f36-45a0-8cf5-303a285ff78d][Communicator#1006][Communicator#1000:Communicator#1023:430:429:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:25:01,940925000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	170	[490b4057-e5d1-43ec-8faf-553fe36f50c5][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:25:01,941276000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	170	[490b4057-e5d1-43ec-8faf-553fe36f50c5][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:25:01,941693000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	170	[490b4057-e5d1-43ec-8faf-553fe36f50c5][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:25:02,398209000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	170	[490b4057-e5d1-43ec-8faf-553fe36f50c5][Communicator#1003][Communicator#1000:Communicator#1002:458:458:457] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:25:02,398551000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	170	[490b4057-e5d1-43ec-8faf-553fe36f50c5][Communicator#1023][Communicator#1000:Communicator#1003:458:458:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:25:02,398978000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	170	[490b4057-e5d1-43ec-8faf-553fe36f50c5][Communicator#1006][Communicator#1000:Communicator#1023:458:458:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:27:02,591813000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[7279655e-9c70-4752-b304-d892a915d662][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:27:02,592474000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[7279655e-9c70-4752-b304-d892a915d662][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:27:02,592998000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[7279655e-9c70-4752-b304-d892a915d662][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:27:03,062159000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[7279655e-9c70-4752-b304-d892a915d662][Communicator#1003][Communicator#1000:Communicator#1002:471:471:470] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:27:03,062503000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[7279655e-9c70-4752-b304-d892a915d662][Communicator#1023][Communicator#1000:Communicator#1003:471:471:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:27:03,062953000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[7279655e-9c70-4752-b304-d892a915d662][Communicator#1006][Communicator#1000:Communicator#1023:471:471:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:28:02,004425000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[0ec5973a-450f-4b64-949d-a97d22188377][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:28:02,004660000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[0ec5973a-450f-4b64-949d-a97d22188377][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:28:02,005071000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[0ec5973a-450f-4b64-949d-a97d22188377][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:28:02,543173000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[0ec5973a-450f-4b64-949d-a97d22188377][Communicator#1003][Communicator#1000:Communicator#1002:539:539:538] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:28:02,543500000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[0ec5973a-450f-4b64-949d-a97d22188377][Communicator#1023][Communicator#1000:Communicator#1003:539:539:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:28:02,543806000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	175	[0ec5973a-450f-4b64-949d-a97d22188377][Communicator#1006][Communicator#1000:Communicator#1023:539:539:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:31:02,095661000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	182	[858a8b75-10f5-40c8-9241-877a0708d667][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:31:02,096014000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	182	[858a8b75-10f5-40c8-9241-877a0708d667][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:31:02,096451000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	182	[858a8b75-10f5-40c8-9241-877a0708d667][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:31:02,126146000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	182	[858a8b75-10f5-40c8-9241-877a0708d667][Communicator#1003][Communicator#1000:Communicator#1002:31:31:30] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:31:02,126401000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	182	[858a8b75-10f5-40c8-9241-877a0708d667][Communicator#1023][Communicator#1000:Communicator#1003:31:31:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:31:02,126785000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	182	[858a8b75-10f5-40c8-9241-877a0708d667][Communicator#1006][Communicator#1000:Communicator#1023:31:31:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:35:01,757576000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	187	[dbc38f71-4f7c-4cdd-a9ff-00936d222e7d][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:35:01,758130000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	187	[dbc38f71-4f7c-4cdd-a9ff-00936d222e7d][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:35:01,758603000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	187	[dbc38f71-4f7c-4cdd-a9ff-00936d222e7d][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:35:02,254564000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	187	[dbc38f71-4f7c-4cdd-a9ff-00936d222e7d][Communicator#1003][Communicator#1000:Communicator#1002:497:497:496] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:35:02,254918000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	187	[dbc38f71-4f7c-4cdd-a9ff-00936d222e7d][Communicator#1023][Communicator#1000:Communicator#1003:497:497:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:35:02,255296000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	187	[dbc38f71-4f7c-4cdd-a9ff-00936d222e7d][Communicator#1006][Communicator#1000:Communicator#1023:498:498:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:36:02,292924000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[0f3194dc-4359-4064-9421-710cac7d2e9c][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:36:02,293277000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[0f3194dc-4359-4064-9421-710cac7d2e9c][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:36:02,293698000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[0f3194dc-4359-4064-9421-710cac7d2e9c][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:36:02,322539000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[0f3194dc-4359-4064-9421-710cac7d2e9c][Communicator#1003][Communicator#1000:Communicator#1002:30:30:29] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:36:02,322788000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[0f3194dc-4359-4064-9421-710cac7d2e9c][Communicator#1023][Communicator#1000:Communicator#1003:30:30:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:36:02,323157000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[0f3194dc-4359-4064-9421-710cac7d2e9c][Communicator#1006][Communicator#1000:Communicator#1023:31:31:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:37:02,282987000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[ab21ead6-c881-4175-a416-f7023521ad55][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:37:02,283188000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[ab21ead6-c881-4175-a416-f7023521ad55][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:37:02,283572000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[ab21ead6-c881-4175-a416-f7023521ad55][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:37:02,305644000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[ab21ead6-c881-4175-a416-f7023521ad55][Communicator#1003][Communicator#1000:Communicator#1002:23:23:22] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:37:02,305869000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[ab21ead6-c881-4175-a416-f7023521ad55][Communicator#1023][Communicator#1000:Communicator#1003:23:23:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:37:02,306126000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	192	[ab21ead6-c881-4175-a416-f7023521ad55][Communicator#1006][Communicator#1000:Communicator#1023:24:24:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:51:02,556424000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	198	[c944e9a4-7b65-4cda-91ca-04dc04d48cfa][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:51:02,556752000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	198	[c944e9a4-7b65-4cda-91ca-04dc04d48cfa][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:51:02,557178000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	198	[c944e9a4-7b65-4cda-91ca-04dc04d48cfa][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:51:03,039271000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	198	[c944e9a4-7b65-4cda-91ca-04dc04d48cfa][Communicator#1003][Communicator#1000:Communicator#1002:483:483:482] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:51:03,039605000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	198	[c944e9a4-7b65-4cda-91ca-04dc04d48cfa][Communicator#1023][Communicator#1000:Communicator#1003:483:483:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:51:03,040042000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	198	[c944e9a4-7b65-4cda-91ca-04dc04d48cfa][Communicator#1006][Communicator#1000:Communicator#1023:483:483:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:53:02,225003000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	203	[ff68266a-79d5-402e-b300-9be87731ac64][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:53:02,225346000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	203	[ff68266a-79d5-402e-b300-9be87731ac64][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:53:02,225770000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	203	[ff68266a-79d5-402e-b300-9be87731ac64][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:53:02,252094000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	203	[ff68266a-79d5-402e-b300-9be87731ac64][Communicator#1003][Communicator#1000:Communicator#1002:28:28:27] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:53:02,252350000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	203	[ff68266a-79d5-402e-b300-9be87731ac64][Communicator#1023][Communicator#1000:Communicator#1003:28:28:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:53:02,252778000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	203	[ff68266a-79d5-402e-b300-9be87731ac64][Communicator#1006][Communicator#1000:Communicator#1023:28:28:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
2025-04-15 01:57:02,138336000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	208	[2ac69e27-1225-4b4f-9024-82cdc27533ca][Communicator#1000][1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] TscCommunicator str
2025-04-15 01:57:02,138647000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	208	[2ac69e27-1225-4b4f-9024-82cdc27533ca][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest str
2025-04-15 01:57:02,139008000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	208	[2ac69e27-1225-4b4f-9024-82cdc27533ca][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] authenticate fin
2025-04-15 01:57:02,166779000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	208	[2ac69e27-1225-4b4f-9024-82cdc27533ca][Communicator#1003][Communicator#1000:Communicator#1002:29:28:28] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] accessCustomerClient fin
2025-04-15 01:57:02,167010000	W	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	208	[2ac69e27-1225-4b4f-9024-82cdc27533ca][Communicator#1023][Communicator#1000:Communicator#1003:29:28:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] CustomerClient Data Error fin
2025-04-15 01:57:02,167370000	I	api-msg-idle-02-84d8b6466d-2cp2p	api-msg	24	208	[2ac69e27-1225-4b4f-9024-82cdc27533ca][Communicator#1006][Communicator#1000:Communicator#1023:30:29:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-2cp2p] invokeRequest fin
