2025-04-17 00:00:00,014134000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	102	[d43b3984-b1f7-4b5e-ac86-1e95f38e8868][Communicator#1000][1] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] UpdateKeyCommunicator str
2025-04-17 00:00:00,019554000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	102	[d43b3984-b1f7-4b5e-ac86-1e95f38e8868][SecretStore#120][Communicator#1000:Communicator#1000:6:5:5] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] update key str
2025-04-17 00:00:00,019707000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	102	[d43b3984-b1f7-4b5e-ac86-1e95f38e8868][SecretStore#204][Communicator#1000:SecretStore#120:6:5:0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] get key list from cloud store
2025-04-17 00:00:00,019801000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	102	[d43b3984-b1f7-4b5e-ac86-1e95f38e8868][SecretStore#207][Communicator#1000:SecretStore#204:6:5:0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] access to cloud store
2025-04-17 00:00:02,238120000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	102	[d43b3984-b1f7-4b5e-ac86-1e95f38e8868][SecretStore#214][Communicator#1000:SecretStore#207:2224:2223:2218] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] save key to cloud store success
2025-04-17 00:00:02,238310000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	102	[d43b3984-b1f7-4b5e-ac86-1e95f38e8868][SecretStore#122][Communicator#1000:SecretStore#214:2225:2224:1] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] update key fin
2025-04-17 00:00:02,238433000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	102	[d43b3984-b1f7-4b5e-ac86-1e95f38e8868][Communicator#1007][Communicator#1000:SecretStore#122:2225:2224:0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] execute fin
2025-04-17 00:06:02,128696000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	115	[4024ec4e-88bc-4431-8387-9fd3a348dcd0][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:06:02,129092000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	115	[4024ec4e-88bc-4431-8387-9fd3a348dcd0][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:06:02,129644000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	115	[4024ec4e-88bc-4431-8387-9fd3a348dcd0][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:06:02,710672000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	115	[4024ec4e-88bc-4431-8387-9fd3a348dcd0][Communicator#1003][Communicator#1000:Communicator#1002:582:582:581] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:06:02,711040000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	115	[4024ec4e-88bc-4431-8387-9fd3a348dcd0][Communicator#1023][Communicator#1000:Communicator#1003:582:582:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:06:02,711619000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	115	[4024ec4e-88bc-4431-8387-9fd3a348dcd0][Communicator#1006][Communicator#1000:Communicator#1023:583:583:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:14:19,894949000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	120	[718df0e4-bd61-4f69-b110-e35e820d6ad6][Communicator#1000][0] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:14:19,895755000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	120	[718df0e4-bd61-4f69-b110-e35e820d6ad6][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:14:19,896336000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	120	[718df0e4-bd61-4f69-b110-e35e820d6ad6][Communicator#1002][Communicator#1000:Communicator#1001:2:2:1] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:14:20,792554000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	120	[718df0e4-bd61-4f69-b110-e35e820d6ad6][Communicator#1003][Communicator#1000:Communicator#1002:898:898:896] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:14:20,792938000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	120	[718df0e4-bd61-4f69-b110-e35e820d6ad6][Communicator#1023][Communicator#1000:Communicator#1003:898:898:0] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:14:20,793401000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	120	[718df0e4-bd61-4f69-b110-e35e820d6ad6][Communicator#1006][Communicator#1000:Communicator#1023:899:899:1] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:18:24,539148000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	125	[4f10b235-5769-41b9-b132-bebdfa509e7f][Communicator#1000][1] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:18:24,539535000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	125	[4f10b235-5769-41b9-b132-bebdfa509e7f][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:18:24,540024000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	125	[4f10b235-5769-41b9-b132-bebdfa509e7f][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:18:25,157837000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	125	[4f10b235-5769-41b9-b132-bebdfa509e7f][Communicator#1003][Communicator#1000:Communicator#1002:619:618:618] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:18:25,158165000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	125	[4f10b235-5769-41b9-b132-bebdfa509e7f][Communicator#1023][Communicator#1000:Communicator#1003:620:619:1] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:18:25,158657000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	125	[4f10b235-5769-41b9-b132-bebdfa509e7f][Communicator#1006][Communicator#1000:Communicator#1023:620:619:0] [220_2_6:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:23:02,371041000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[6fff1645-e67c-4a57-afdc-e37d39bbe0a8][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:23:02,371424000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[6fff1645-e67c-4a57-afdc-e37d39bbe0a8][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:23:02,371933000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[6fff1645-e67c-4a57-afdc-e37d39bbe0a8][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:23:02,398347000	E	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[6fff1645-e67c-4a57-afdc-e37d39bbe0a8][Communicator#1022][Communicator#1000:Communicator#1002:28:28:27] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Access Error fin
2025-04-17 00:23:02,399498000	F	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[6fff1645-e67c-4a57-afdc-e37d39bbe0a8][Communicator#999][Communicator#1000:Communicator#1022:28:28:0] java.util.concurrent.CompletionException: java.net.ConnectException: Connection refused
	java.base/java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:367)
	java.base/java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:376)
	java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1074)
	java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:506)
	java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2088)
	java.net.http/jdk.internal.net.http.PlainHttpConnection$ConnectEvent.lambda$handle$1(PlainHttpConnection.java:137)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-17 00:23:02,399978000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[6fff1645-e67c-4a57-afdc-e37d39bbe0a8][Communicator#1006][Communicator#1000:Communicator#?:29:29:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:24:02,336053000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[73fab51f-4eb7-4f5b-9087-95b2f692968a][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:24:02,336244000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[73fab51f-4eb7-4f5b-9087-95b2f692968a][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:24:02,336658000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[73fab51f-4eb7-4f5b-9087-95b2f692968a][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:24:02,782622000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[73fab51f-4eb7-4f5b-9087-95b2f692968a][Communicator#1003][Communicator#1000:Communicator#1002:447:447:446] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:24:02,782964000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[73fab51f-4eb7-4f5b-9087-95b2f692968a][Communicator#1023][Communicator#1000:Communicator#1003:447:447:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:24:02,783326000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[73fab51f-4eb7-4f5b-9087-95b2f692968a][Communicator#1006][Communicator#1000:Communicator#1023:448:448:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:25:02,320765000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[850de6d1-e5fa-4998-9cfe-5ebbcd1f7324][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:25:02,320983000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[850de6d1-e5fa-4998-9cfe-5ebbcd1f7324][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:25:02,321468000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[850de6d1-e5fa-4998-9cfe-5ebbcd1f7324][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:25:02,805769000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[850de6d1-e5fa-4998-9cfe-5ebbcd1f7324][Communicator#1003][Communicator#1000:Communicator#1002:485:485:484] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:25:02,806125000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[850de6d1-e5fa-4998-9cfe-5ebbcd1f7324][Communicator#1023][Communicator#1000:Communicator#1003:486:486:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:25:02,806450000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	130	[850de6d1-e5fa-4998-9cfe-5ebbcd1f7324][Communicator#1006][Communicator#1000:Communicator#1023:486:486:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:30:00,007179000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	138	[4f766d48-f667-4efc-ac6c-8666cb81cfdc][Communicator#1000][1] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] SaveKeyCommunicator str
2025-04-17 00:30:00,007563000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	138	[4f766d48-f667-4efc-ac6c-8666cb81cfdc][SecretStore#110][Communicator#1000:Communicator#1000:1:0:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] save next key str
2025-04-17 00:30:00,007937000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	138	[4f766d48-f667-4efc-ac6c-8666cb81cfdc][SecretStore#204][Communicator#1000:SecretStore#110:1:0:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] get key list from cloud store
2025-04-17 00:30:00,008052000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	138	[4f766d48-f667-4efc-ac6c-8666cb81cfdc][SecretStore#207][Communicator#1000:SecretStore#204:2:1:1] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] access to cloud store
2025-04-17 00:30:01,393419000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	138	[4f766d48-f667-4efc-ac6c-8666cb81cfdc][SecretStore#111][Communicator#1000:SecretStore#207:1387:1386:1385] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] save next key to local file
2025-04-17 00:30:01,394405000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	138	[4f766d48-f667-4efc-ac6c-8666cb81cfdc][SecretStore#210][Communicator#1000:SecretStore#111:1388:1387:1] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] save key to local file success
2025-04-17 00:30:01,394742000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	138	[4f766d48-f667-4efc-ac6c-8666cb81cfdc][SecretStore#113][Communicator#1000:SecretStore#210:1388:1387:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] save next key fin
2025-04-17 00:30:01,394868000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	138	[4f766d48-f667-4efc-ac6c-8666cb81cfdc][Communicator#1007][Communicator#1000:SecretStore#113:1388:1387:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-n5fhc] execute fin
2025-04-17 00:32:02,400919000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	144	[e2af9d84-4bd8-4e56-87c4-9762d941b094][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:32:02,401248000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	144	[e2af9d84-4bd8-4e56-87c4-9762d941b094][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:32:02,401699000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	144	[e2af9d84-4bd8-4e56-87c4-9762d941b094][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:32:03,462488000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	144	[e2af9d84-4bd8-4e56-87c4-9762d941b094][Communicator#1003][Communicator#1000:Communicator#1002:1062:1062:1061] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:32:03,462786000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	144	[e2af9d84-4bd8-4e56-87c4-9762d941b094][Communicator#1023][Communicator#1000:Communicator#1003:1062:1062:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:32:03,463207000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	144	[e2af9d84-4bd8-4e56-87c4-9762d941b094][Communicator#1006][Communicator#1000:Communicator#1023:1063:1063:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:43:03,019318000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	149	[961d1945-9bf4-41bf-902e-3cc5f4368bf5][Communicator#1000][1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:43:03,019652000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	149	[961d1945-9bf4-41bf-902e-3cc5f4368bf5][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:43:03,020126000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	149	[961d1945-9bf4-41bf-902e-3cc5f4368bf5][Communicator#1002][Communicator#1000:Communicator#1001:2:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:43:03,493268000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	149	[961d1945-9bf4-41bf-902e-3cc5f4368bf5][Communicator#1003][Communicator#1000:Communicator#1002:475:474:473] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:43:03,493536000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	149	[961d1945-9bf4-41bf-902e-3cc5f4368bf5][Communicator#1023][Communicator#1000:Communicator#1003:475:474:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:43:03,493995000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	149	[961d1945-9bf4-41bf-902e-3cc5f4368bf5][Communicator#1006][Communicator#1000:Communicator#1023:475:474:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:45:02,468981000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	154	[47e347e7-3146-40d9-b480-3bbd40415254][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:45:02,469317000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	154	[47e347e7-3146-40d9-b480-3bbd40415254][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:45:02,469815000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	154	[47e347e7-3146-40d9-b480-3bbd40415254][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:45:03,450930000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	154	[47e347e7-3146-40d9-b480-3bbd40415254][Communicator#1003][Communicator#1000:Communicator#1002:982:982:981] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:45:03,451210000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	154	[47e347e7-3146-40d9-b480-3bbd40415254][Communicator#1023][Communicator#1000:Communicator#1003:983:983:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:45:03,451629000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	154	[47e347e7-3146-40d9-b480-3bbd40415254][Communicator#1006][Communicator#1000:Communicator#1023:983:983:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:50:01,837522000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	159	[3171b34a-1c29-4dcc-92fa-8a40f33c812b][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:50:01,837851000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	159	[3171b34a-1c29-4dcc-92fa-8a40f33c812b][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:50:01,838247000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	159	[3171b34a-1c29-4dcc-92fa-8a40f33c812b][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:50:02,854901000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	159	[3171b34a-1c29-4dcc-92fa-8a40f33c812b][Communicator#1003][Communicator#1000:Communicator#1002:1017:1017:1016] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:50:02,855212000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	159	[3171b34a-1c29-4dcc-92fa-8a40f33c812b][Communicator#1023][Communicator#1000:Communicator#1003:1018:1018:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:50:02,855625000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	159	[3171b34a-1c29-4dcc-92fa-8a40f33c812b][Communicator#1006][Communicator#1000:Communicator#1023:1018:1018:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
2025-04-17 00:53:02,034014000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	164	[3c2ef397-7626-44bc-af24-cf445dbbd521][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] TscCommunicator str
2025-04-17 00:53:02,034343000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	164	[3c2ef397-7626-44bc-af24-cf445dbbd521][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest str
2025-04-17 00:53:02,034813000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	164	[3c2ef397-7626-44bc-af24-cf445dbbd521][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] authenticate fin
2025-04-17 00:53:02,939874000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	164	[3c2ef397-7626-44bc-af24-cf445dbbd521][Communicator#1003][Communicator#1000:Communicator#1002:906:906:905] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] accessCustomerClient fin
2025-04-17 00:53:02,940167000	W	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	164	[3c2ef397-7626-44bc-af24-cf445dbbd521][Communicator#1023][Communicator#1000:Communicator#1003:907:907:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] CustomerClient Data Error fin
2025-04-17 00:53:02,940638000	I	api-msg-idle-01-55d9b47b86-n5fhc	api-msg	24	164	[3c2ef397-7626-44bc-af24-cf445dbbd521][Communicator#1006][Communicator#1000:Communicator#1023:907:907:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-n5fhc] invokeRequest fin
