2025-04-16 00:00:00,008213000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[334600ec-bd24-415b-944e-678c072ae360][Communicator#1000][1] [updateEncryptionKeyMsg api-msg-idle-02-84d8b6466d-9v4tr] UpdateKeyCommunicator str
2025-04-16 00:00:00,014002000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[334600ec-bd24-415b-944e-678c072ae360][Communicator#1007][Communicator#1000:Communicator#1000:6:5:5] [updateEncryptionKeyMsg api-msg-idle-02-84d8b6466d-9v4tr] execute fin
2025-04-16 00:00:14,013311000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][Communicator#1000][1] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] AppCommunicator str
2025-04-16 00:00:14,014647000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][Communicator#1001][Communicator#1000:Communicator#1000:2:1:1] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest str
2025-04-16 00:00:14,018327000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#100][Communicator#1000:Communicator#1001:6:5:4] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] get key str
2025-04-16 00:00:14,018766000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#101][Communicator#1000:SecretStore#100:6:5:0] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] get key list from local file
2025-04-16 00:00:14,018999000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#201][Communicator#1000:SecretStore#101:6:5:0] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] no local file for today
2025-04-16 00:00:14,019332000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#202][Communicator#1000:SecretStore#201:7:6:1] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] get key list from store
2025-04-16 00:00:14,019409000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#204][Communicator#1000:SecretStore#202:7:6:0] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] get key list from cloud store
2025-04-16 00:00:14,019475000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#207][Communicator#1000:SecretStore#204:7:6:0] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] access to cloud store
2025-04-16 00:00:15,668721000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#208][Communicator#1000:SecretStore#207:1656:1655:1649] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] save first key to local file
2025-04-16 00:00:15,669826000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#210][Communicator#1000:SecretStore#208:1657:1656:1] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] save key to local file success
2025-04-16 00:00:15,669957000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#209][Communicator#1000:SecretStore#210:1657:1656:0] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] save second key to local file
2025-04-16 00:00:15,670169000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#210][Communicator#1000:SecretStore#209:1658:1657:1] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] save key to local file success
2025-04-16 00:00:15,670397000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][SecretStore#102][Communicator#1000:SecretStore#210:1658:1657:0] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] get key fin
2025-04-16 00:00:15,670963000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][Communicator#1013][Communicator#1000:SecretStore#102:1658:1657:0] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] Can not decrypt encrypted request body. fin
2025-04-16 00:00:15,671714000	F	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][Communicator#999][Communicator#1000:Communicator#1013:1659:1658:1] javax.crypto.BadPaddingException: Given final block not properly padded. Such issues can arise if a bad key is used during decryption.
	java.base/com.sun.crypto.provider.CipherCore.unpad(CipherCore.java:975)
	java.base/com.sun.crypto.provider.CipherCore.fillOutputBuffer(CipherCore.java:1056)
	java.base/com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:853)
	java.base/com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	java.base/javax.crypto.Cipher.doFinal(Cipher.java:2202)
	mazda.tk2.api.common.imp.ctl.AppCryptography.decryption(AppCryptography.java:82)
	mazda.tk2.api.common.imp.ctl.AppCommunicator.invokeRequest(AppCommunicator.java:135)
	messagereceiver.DoorLock.run(DoorLock.java:42)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-16 00:00:15,678421000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][ConnectionWithSqlDb#1001][Communicator#1000:Communicator#?:1666:1665:7] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] db connection create str
2025-04-16 00:00:15,692955000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][ConnectionWithSqlDb#1003][Communicator#1000:ConnectionWithSqlDb#1001:1680:1679:14] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] db token create str
2025-04-16 00:00:15,984556000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][ConnectionWithSqlDb#1004][Communicator#1000:ConnectionWithSqlDb#1003:1972:1971:292] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] db token create fin
2025-04-16 00:00:16,115990000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][ConnectionWithSqlDb#1002][Communicator#1000:ConnectionWithSqlDb#1004:2103:2102:131] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] db connection create fin
2025-04-16 00:00:16,229286000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[d9e0c9ab-40b4-4d66-8baf-a668387b9c03][Communicator#1006][Communicator#1000:ConnectionWithSqlDb#1002:2217:2216:114] [130_2_3:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest fin
2025-04-16 00:00:22,511357000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[64a415f2-a3a0-4d8c-b995-b3ab0cb716a7][Communicator#1000][1] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] TscCommunicator str
2025-04-16 00:00:22,511566000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[64a415f2-a3a0-4d8c-b995-b3ab0cb716a7][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest str
2025-04-16 00:00:22,512541000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[64a415f2-a3a0-4d8c-b995-b3ab0cb716a7][Communicator#1002][Communicator#1000:Communicator#1001:2:1:1] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] authenticate fin
2025-04-16 00:00:23,455548000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[64a415f2-a3a0-4d8c-b995-b3ab0cb716a7][Communicator#1003][Communicator#1000:Communicator#1002:945:944:943] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] accessCustomerClient fin
2025-04-16 00:00:23,455859000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[64a415f2-a3a0-4d8c-b995-b3ab0cb716a7][Communicator#1023][Communicator#1000:Communicator#1003:945:944:0] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] CustomerClient Data Error fin
2025-04-16 00:00:23,456156000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[64a415f2-a3a0-4d8c-b995-b3ab0cb716a7][Communicator#1006][Communicator#1000:Communicator#1023:946:945:1] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest fin
2025-04-16 00:00:39,069775000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[b4cc9bc2-643a-471e-bab8-de4f5c6d0e6b][Communicator#1000][0] [220_2_6:20:* api-msg-idle-02-84d8b6466d-9v4tr] TscCommunicator str
2025-04-16 00:00:39,069997000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[b4cc9bc2-643a-471e-bab8-de4f5c6d0e6b][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [220_2_6:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest str
2025-04-16 00:00:39,070938000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[b4cc9bc2-643a-471e-bab8-de4f5c6d0e6b][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [220_2_6:20:* api-msg-idle-02-84d8b6466d-9v4tr] authenticate fin
2025-04-16 00:00:39,633171000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[b4cc9bc2-643a-471e-bab8-de4f5c6d0e6b][Communicator#1003][Communicator#1000:Communicator#1002:564:564:563] [220_2_6:20:* api-msg-idle-02-84d8b6466d-9v4tr] accessCustomerClient fin
2025-04-16 00:00:39,633499000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[b4cc9bc2-643a-471e-bab8-de4f5c6d0e6b][Communicator#1023][Communicator#1000:Communicator#1003:564:564:0] [220_2_6:20:* api-msg-idle-02-84d8b6466d-9v4tr] CustomerClient Data Error fin
2025-04-16 00:00:39,633797000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[b4cc9bc2-643a-471e-bab8-de4f5c6d0e6b][Communicator#1006][Communicator#1000:Communicator#1023:564:564:0] [220_2_6:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest fin
2025-04-16 00:01:02,389482000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[3002e937-4ae5-4bd9-ad29-e2f7448e13ec][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] TscCommunicator str
2025-04-16 00:01:02,389705000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[3002e937-4ae5-4bd9-ad29-e2f7448e13ec][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest str
2025-04-16 00:01:02,390162000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[3002e937-4ae5-4bd9-ad29-e2f7448e13ec][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] authenticate fin
2025-04-16 00:01:02,920927000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[3002e937-4ae5-4bd9-ad29-e2f7448e13ec][Communicator#1003][Communicator#1000:Communicator#1002:531:531:530] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] accessCustomerClient fin
2025-04-16 00:01:02,921210000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[3002e937-4ae5-4bd9-ad29-e2f7448e13ec][Communicator#1023][Communicator#1000:Communicator#1003:532:532:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] CustomerClient Data Error fin
2025-04-16 00:01:02,921473000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	129	[3002e937-4ae5-4bd9-ad29-e2f7448e13ec][Communicator#1006][Communicator#1000:Communicator#1023:532:532:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest fin
2025-04-16 00:03:27,970246000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	149	[16988725-0ec0-43af-82ed-14d11cd29f7a][Communicator#1000][1] [70_2_2:20:* api-msg-idle-02-84d8b6466d-9v4tr] TscCommunicator str
2025-04-16 00:03:27,970593000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	149	[16988725-0ec0-43af-82ed-14d11cd29f7a][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [70_2_2:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest str
2025-04-16 00:03:27,971114000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	149	[16988725-0ec0-43af-82ed-14d11cd29f7a][Communicator#1002][Communicator#1000:Communicator#1001:2:1:1] [70_2_2:20:* api-msg-idle-02-84d8b6466d-9v4tr] authenticate fin
2025-04-16 00:03:28,014815000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	149	[16988725-0ec0-43af-82ed-14d11cd29f7a][Communicator#1003][Communicator#1000:Communicator#1002:45:44:43] [70_2_2:20:* api-msg-idle-02-84d8b6466d-9v4tr] accessCustomerClient fin
2025-04-16 00:03:28,015032000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	149	[16988725-0ec0-43af-82ed-14d11cd29f7a][Communicator#1023][Communicator#1000:Communicator#1003:45:44:0] [70_2_2:20:* api-msg-idle-02-84d8b6466d-9v4tr] CustomerClient Data Error fin
2025-04-16 00:03:28,015364000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	149	[16988725-0ec0-43af-82ed-14d11cd29f7a][Communicator#1006][Communicator#1000:Communicator#1023:46:45:1] [70_2_2:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest fin
2025-04-16 00:08:02,937199000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	154	[5b6576f8-cf94-4dac-ae74-ee5ba3da58fe][Communicator#1000][1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] TscCommunicator str
2025-04-16 00:08:02,937559000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	154	[5b6576f8-cf94-4dac-ae74-ee5ba3da58fe][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest str
2025-04-16 00:08:02,937999000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	154	[5b6576f8-cf94-4dac-ae74-ee5ba3da58fe][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] authenticate fin
2025-04-16 00:08:03,501707000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	154	[5b6576f8-cf94-4dac-ae74-ee5ba3da58fe][Communicator#1003][Communicator#1000:Communicator#1002:565:564:564] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] accessCustomerClient fin
2025-04-16 00:08:03,501989000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	154	[5b6576f8-cf94-4dac-ae74-ee5ba3da58fe][Communicator#1023][Communicator#1000:Communicator#1003:565:564:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] CustomerClient Data Error fin
2025-04-16 00:08:03,502376000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	154	[5b6576f8-cf94-4dac-ae74-ee5ba3da58fe][Communicator#1006][Communicator#1000:Communicator#1023:566:565:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest fin
2025-04-16 00:14:24,873364000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	159	[ae93f3c0-32d2-41cd-bd5f-8306e6f78a97][Communicator#1000][1] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] TscCommunicator str
2025-04-16 00:14:24,873741000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	159	[ae93f3c0-32d2-41cd-bd5f-8306e6f78a97][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest str
2025-04-16 00:14:24,874495000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	159	[ae93f3c0-32d2-41cd-bd5f-8306e6f78a97][Communicator#1002][Communicator#1000:Communicator#1001:2:1:1] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] authenticate fin
2025-04-16 00:14:25,416693000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	159	[ae93f3c0-32d2-41cd-bd5f-8306e6f78a97][Communicator#1003][Communicator#1000:Communicator#1002:544:543:542] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] accessCustomerClient fin
2025-04-16 00:14:25,416982000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	159	[ae93f3c0-32d2-41cd-bd5f-8306e6f78a97][Communicator#1023][Communicator#1000:Communicator#1003:544:543:0] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] CustomerClient Data Error fin
2025-04-16 00:14:25,417430000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	159	[ae93f3c0-32d2-41cd-bd5f-8306e6f78a97][Communicator#1006][Communicator#1000:Communicator#1023:545:544:1] [70_2_2:24:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest fin
2025-04-16 00:24:02,045004000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	164	[83aad06e-5c73-4cc1-8a56-a1fcd73bef96][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] TscCommunicator str
2025-04-16 00:24:02,045328000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	164	[83aad06e-5c73-4cc1-8a56-a1fcd73bef96][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest str
2025-04-16 00:24:02,045834000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	164	[83aad06e-5c73-4cc1-8a56-a1fcd73bef96][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] authenticate fin
2025-04-16 00:24:03,079117000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	164	[83aad06e-5c73-4cc1-8a56-a1fcd73bef96][Communicator#1003][Communicator#1000:Communicator#1002:1035:1035:1034] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] accessCustomerClient fin
2025-04-16 00:24:03,079402000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	164	[83aad06e-5c73-4cc1-8a56-a1fcd73bef96][Communicator#1023][Communicator#1000:Communicator#1003:1035:1035:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] CustomerClient Data Error fin
2025-04-16 00:24:03,079790000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	164	[83aad06e-5c73-4cc1-8a56-a1fcd73bef96][Communicator#1006][Communicator#1000:Communicator#1023:1035:1035:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest fin
2025-04-16 00:30:00,003561000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	169	[b991d412-ace2-4034-8d06-c4f71c1b60cc][Communicator#1000][0] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-9v4tr] SaveKeyCommunicator str
2025-04-16 00:30:00,004137000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	169	[b991d412-ace2-4034-8d06-c4f71c1b60cc][SecretStore#110][Communicator#1000:Communicator#1000:1:1:1] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-9v4tr] save next key str
2025-04-16 00:30:00,004593000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	169	[b991d412-ace2-4034-8d06-c4f71c1b60cc][SecretStore#112][Communicator#1000:SecretStore#110:1:1:0] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-9v4tr] next key is already exists
2025-04-16 00:30:00,004962000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	169	[b991d412-ace2-4034-8d06-c4f71c1b60cc][SecretStore#113][Communicator#1000:SecretStore#112:1:1:0] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-9v4tr] save next key fin
2025-04-16 00:30:00,005150000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	169	[b991d412-ace2-4034-8d06-c4f71c1b60cc][Communicator#1007][Communicator#1000:SecretStore#113:2:2:1] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-9v4tr] execute fin
2025-04-16 00:36:02,847423000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	171	[ad906de2-bba3-4da8-88c1-064ecb5c05ab][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] TscCommunicator str
2025-04-16 00:36:02,848088000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	171	[ad906de2-bba3-4da8-88c1-064ecb5c05ab][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest str
2025-04-16 00:36:02,848583000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	171	[ad906de2-bba3-4da8-88c1-064ecb5c05ab][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] authenticate fin
2025-04-16 00:36:03,240338000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	171	[ad906de2-bba3-4da8-88c1-064ecb5c05ab][Communicator#1003][Communicator#1000:Communicator#1002:393:393:392] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] accessCustomerClient fin
2025-04-16 00:36:03,240620000	W	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	171	[ad906de2-bba3-4da8-88c1-064ecb5c05ab][Communicator#1023][Communicator#1000:Communicator#1003:393:393:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] CustomerClient Data Error fin
2025-04-16 00:36:03,241058000	I	api-msg-idle-02-84d8b6466d-9v4tr	api-msg	24	171	[ad906de2-bba3-4da8-88c1-064ecb5c05ab][Communicator#1006][Communicator#1000:Communicator#1023:394:394:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-9v4tr] invokeRequest fin
