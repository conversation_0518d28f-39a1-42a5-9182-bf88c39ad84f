2025-04-15 08:00:00,003022000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[fc01ca94-9779-43e7-98e6-f2073a837a7b][Communicator#1000][0] [updateEncryptionKeyMsg api-msg-idle-02-84d8b6466d-xltkc] UpdateKeyCommunicator str
2025-04-15 08:00:00,003215000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[fc01ca94-9779-43e7-98e6-f2073a837a7b][Communicator#1007][Communicator#1000:Communicator#1000:1:1:1] [updateEncryptionKeyMsg api-msg-idle-02-84d8b6466d-xltkc] execute fin
2025-04-15 08:00:13,094284000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][Communicator#1000][1] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] AppCommunicator str
2025-04-15 08:00:13,095382000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][Communicator#1001][Communicator#1000:Communicator#1000:2:1:1] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest str
2025-04-15 08:00:13,095958000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#100][Communicator#1000:Communicator#1001:2:1:0] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] get key str
2025-04-15 08:00:13,096163000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#101][Communicator#1000:SecretStore#100:3:2:1] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] get key list from local file
2025-04-15 08:00:13,096327000	W	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#201][Communicator#1000:SecretStore#101:3:2:0] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] no local file for today
2025-04-15 08:00:13,096430000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#202][Communicator#1000:SecretStore#201:3:2:0] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] get key list from store
2025-04-15 08:00:13,096450000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#204][Communicator#1000:SecretStore#202:3:2:0] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] get key list from cloud store
2025-04-15 08:00:13,096467000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#207][Communicator#1000:SecretStore#204:3:2:0] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] access to cloud store
2025-04-15 08:00:13,643495000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#208][Communicator#1000:SecretStore#207:550:549:547] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] save first key to local file
2025-04-15 08:00:13,643853000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#210][Communicator#1000:SecretStore#208:550:549:0] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] save key to local file success
2025-04-15 08:00:13,643891000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#209][Communicator#1000:SecretStore#210:550:549:0] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] save second key to local file
2025-04-15 08:00:13,644117000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#210][Communicator#1000:SecretStore#209:551:550:1] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] save key to local file success
2025-04-15 08:00:13,644298000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][SecretStore#102][Communicator#1000:SecretStore#210:551:550:0] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] get key fin
2025-04-15 08:00:13,644983000	W	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][Communicator#1013][Communicator#1000:SecretStore#102:551:550:0] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] Can not decrypt encrypted request body. fin
2025-04-15 08:00:13,645294000	F	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][Communicator#999][Communicator#1000:Communicator#1013:552:551:1] javax.crypto.BadPaddingException: Given final block not properly padded. Such issues can arise if a bad key is used during decryption.
	java.base/com.sun.crypto.provider.CipherCore.unpad(CipherCore.java:975)
	java.base/com.sun.crypto.provider.CipherCore.fillOutputBuffer(CipherCore.java:1056)
	java.base/com.sun.crypto.provider.CipherCore.doFinal(CipherCore.java:853)
	java.base/com.sun.crypto.provider.AESCipher.engineDoFinal(AESCipher.java:446)
	java.base/javax.crypto.Cipher.doFinal(Cipher.java:2202)
	mazda.tk2.api.common.imp.ctl.AppCryptography.decryption(AppCryptography.java:82)
	mazda.tk2.api.common.imp.ctl.AppCommunicator.invokeRequest(AppCommunicator.java:135)
	messagereceiver.SendPoi.run(SendPoi.java:42)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-15 08:00:13,654034000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][ConnectionWithSqlDb#1001][Communicator#1000:Communicator#?:560:559:8] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] db connection create str
2025-04-15 08:00:13,668287000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][ConnectionWithSqlDb#1003][Communicator#1000:ConnectionWithSqlDb#1001:575:574:15] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] db token create str
2025-04-15 08:00:14,070571000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][ConnectionWithSqlDb#1004][Communicator#1000:ConnectionWithSqlDb#1003:977:976:402] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] db token create fin
2025-04-15 08:00:14,212137000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][ConnectionWithSqlDb#1002][Communicator#1000:ConnectionWithSqlDb#1004:1119:1118:142] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] db connection create fin
2025-04-15 08:00:14,277238000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[6891a346-7fbe-4c02-9ec7-b9b9340f4765][Communicator#1006][Communicator#1000:ConnectionWithSqlDb#1002:1184:1183:65] [110_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest fin
2025-04-15 08:01:01,951934000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[92ae1ebf-211f-405b-af81-233ab6540976][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] TscCommunicator str
2025-04-15 08:01:01,952115000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[92ae1ebf-211f-405b-af81-233ab6540976][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest str
2025-04-15 08:01:01,952467000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[92ae1ebf-211f-405b-af81-233ab6540976][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] authenticate fin
2025-04-15 08:01:01,978871000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[92ae1ebf-211f-405b-af81-233ab6540976][Communicator#1003][Communicator#1000:Communicator#1002:27:27:26] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] accessCustomerClient fin
2025-04-15 08:01:01,979018000	W	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[92ae1ebf-211f-405b-af81-233ab6540976][Communicator#1023][Communicator#1000:Communicator#1003:27:27:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] CustomerClient Data Error fin
2025-04-15 08:01:01,979233000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	558	[92ae1ebf-211f-405b-af81-233ab6540976][Communicator#1006][Communicator#1000:Communicator#1023:28:28:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest fin
2025-04-15 08:22:38,725348000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	570	[7daac21f-9ae7-4b65-a19e-74bf57a8e5f7][Communicator#1000][0] [130_2_99:24:* api-msg-idle-02-84d8b6466d-xltkc] TscCommunicator str
2025-04-15 08:22:38,725558000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	570	[7daac21f-9ae7-4b65-a19e-74bf57a8e5f7][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [130_2_99:24:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest str
2025-04-15 08:22:38,725856000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	570	[7daac21f-9ae7-4b65-a19e-74bf57a8e5f7][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [130_2_99:24:* api-msg-idle-02-84d8b6466d-xltkc] authenticate fin
2025-04-15 08:22:38,756082000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	570	[7daac21f-9ae7-4b65-a19e-74bf57a8e5f7][Communicator#1003][Communicator#1000:Communicator#1002:31:31:31] [130_2_99:24:* api-msg-idle-02-84d8b6466d-xltkc] accessCustomerClient fin
2025-04-15 08:22:38,756191000	W	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	570	[7daac21f-9ae7-4b65-a19e-74bf57a8e5f7][Communicator#1023][Communicator#1000:Communicator#1003:31:31:0] [130_2_99:24:* api-msg-idle-02-84d8b6466d-xltkc] CustomerClient Data Error fin
2025-04-15 08:22:38,756411000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	570	[7daac21f-9ae7-4b65-a19e-74bf57a8e5f7][Communicator#1006][Communicator#1000:Communicator#1023:31:31:0] [130_2_99:24:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest fin
2025-04-15 08:30:00,003466000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	575	[5b6d80e1-1888-4162-9a38-bdad9751c7ed][Communicator#1000][0] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-xltkc] SaveKeyCommunicator str
2025-04-15 08:30:00,003727000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	575	[5b6d80e1-1888-4162-9a38-bdad9751c7ed][SecretStore#110][Communicator#1000:Communicator#1000:0:0:0] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-xltkc] save next key str
2025-04-15 08:30:00,003837000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	575	[5b6d80e1-1888-4162-9a38-bdad9751c7ed][SecretStore#112][Communicator#1000:SecretStore#110:0:0:0] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-xltkc] next key is already exists
2025-04-15 08:30:00,003890000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	575	[5b6d80e1-1888-4162-9a38-bdad9751c7ed][SecretStore#113][Communicator#1000:SecretStore#112:0:0:0] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-xltkc] save next key fin
2025-04-15 08:30:00,003919000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	575	[5b6d80e1-1888-4162-9a38-bdad9751c7ed][Communicator#1007][Communicator#1000:SecretStore#113:0:0:0] [saveEncryptionKeyMsg api-msg-idle-02-84d8b6466d-xltkc] execute fin
2025-04-15 08:36:01,774933000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	577	[7dd388e5-e47c-469a-be18-8b778f11864a][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] TscCommunicator str
2025-04-15 08:36:01,775160000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	577	[7dd388e5-e47c-469a-be18-8b778f11864a][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest str
2025-04-15 08:36:01,775433000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	577	[7dd388e5-e47c-469a-be18-8b778f11864a][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] authenticate fin
2025-04-15 08:36:01,802189000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	577	[7dd388e5-e47c-469a-be18-8b778f11864a][Communicator#1003][Communicator#1000:Communicator#1002:28:28:27] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] accessCustomerClient fin
2025-04-15 08:36:01,802315000	W	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	577	[7dd388e5-e47c-469a-be18-8b778f11864a][Communicator#1023][Communicator#1000:Communicator#1003:28:28:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] CustomerClient Data Error fin
2025-04-15 08:36:01,802582000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	577	[7dd388e5-e47c-469a-be18-8b778f11864a][Communicator#1006][Communicator#1000:Communicator#1023:28:28:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest fin
2025-04-15 08:42:02,222631000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	582	[07f09a59-818a-4abe-8962-b54b524a86aa][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] TscCommunicator str
2025-04-15 08:42:02,222824000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	582	[07f09a59-818a-4abe-8962-b54b524a86aa][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest str
2025-04-15 08:42:02,223058000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	582	[07f09a59-818a-4abe-8962-b54b524a86aa][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] authenticate fin
2025-04-15 08:42:02,250965000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	582	[07f09a59-818a-4abe-8962-b54b524a86aa][Communicator#1003][Communicator#1000:Communicator#1002:28:28:27] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] accessCustomerClient fin
2025-04-15 08:42:02,251096000	W	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	582	[07f09a59-818a-4abe-8962-b54b524a86aa][Communicator#1023][Communicator#1000:Communicator#1003:29:29:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] CustomerClient Data Error fin
2025-04-15 08:42:02,251405000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	582	[07f09a59-818a-4abe-8962-b54b524a86aa][Communicator#1006][Communicator#1000:Communicator#1023:29:29:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest fin
2025-04-15 08:43:02,323351000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	587	[d74a5f5f-bfda-4efb-ba16-1e2179a9cd7f][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] TscCommunicator str
2025-04-15 08:43:02,323515000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	587	[d74a5f5f-bfda-4efb-ba16-1e2179a9cd7f][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest str
2025-04-15 08:43:02,323829000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	587	[d74a5f5f-bfda-4efb-ba16-1e2179a9cd7f][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] authenticate fin
2025-04-15 08:43:02,347962000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	587	[d74a5f5f-bfda-4efb-ba16-1e2179a9cd7f][Communicator#1003][Communicator#1000:Communicator#1002:24:24:24] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] accessCustomerClient fin
2025-04-15 08:43:02,348073000	W	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	587	[d74a5f5f-bfda-4efb-ba16-1e2179a9cd7f][Communicator#1023][Communicator#1000:Communicator#1003:25:25:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] CustomerClient Data Error fin
2025-04-15 08:43:02,348279000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	587	[d74a5f5f-bfda-4efb-ba16-1e2179a9cd7f][Communicator#1006][Communicator#1000:Communicator#1023:25:25:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest fin
2025-04-15 08:51:10,333446000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	592	[c2e4d05b-702d-4b51-830f-73656ad6e074][Communicator#1000][0] [70_2_2:24:* api-msg-idle-02-84d8b6466d-xltkc] TscCommunicator str
2025-04-15 08:51:10,333611000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	592	[c2e4d05b-702d-4b51-830f-73656ad6e074][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [70_2_2:24:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest str
2025-04-15 08:51:10,334015000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	592	[c2e4d05b-702d-4b51-830f-73656ad6e074][Communicator#1002][Communicator#1000:Communicator#1001:0:0:0] [70_2_2:24:* api-msg-idle-02-84d8b6466d-xltkc] authenticate fin
2025-04-15 08:51:10,375648000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	592	[c2e4d05b-702d-4b51-830f-73656ad6e074][Communicator#1003][Communicator#1000:Communicator#1002:42:42:42] [70_2_2:24:* api-msg-idle-02-84d8b6466d-xltkc] accessCustomerClient fin
2025-04-15 08:51:10,375762000	W	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	592	[c2e4d05b-702d-4b51-830f-73656ad6e074][Communicator#1023][Communicator#1000:Communicator#1003:42:42:0] [70_2_2:24:* api-msg-idle-02-84d8b6466d-xltkc] CustomerClient Data Error fin
2025-04-15 08:51:10,375975000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	592	[c2e4d05b-702d-4b51-830f-73656ad6e074][Communicator#1006][Communicator#1000:Communicator#1023:42:42:0] [70_2_2:24:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest fin
2025-04-15 08:59:02,328979000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	597	[fd6184fa-bc38-4dd1-94fe-88d282df3bd9][Communicator#1000][0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] TscCommunicator str
2025-04-15 08:59:02,329152000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	597	[fd6184fa-bc38-4dd1-94fe-88d282df3bd9][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest str
2025-04-15 08:59:02,329395000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	597	[fd6184fa-bc38-4dd1-94fe-88d282df3bd9][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] authenticate fin
2025-04-15 08:59:02,354769000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	597	[fd6184fa-bc38-4dd1-94fe-88d282df3bd9][Communicator#1003][Communicator#1000:Communicator#1002:26:26:25] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] accessCustomerClient fin
2025-04-15 08:59:02,354882000	W	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	597	[fd6184fa-bc38-4dd1-94fe-88d282df3bd9][Communicator#1023][Communicator#1000:Communicator#1003:26:26:0] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] CustomerClient Data Error fin
2025-04-15 08:59:02,355110000	I	api-msg-idle-02-84d8b6466d-xltkc	api-msg	23	597	[fd6184fa-bc38-4dd1-94fe-88d282df3bd9][Communicator#1006][Communicator#1000:Communicator#1023:27:27:1] [20_2_1:20:* api-msg-idle-02-84d8b6466d-xltkc] invokeRequest fin
