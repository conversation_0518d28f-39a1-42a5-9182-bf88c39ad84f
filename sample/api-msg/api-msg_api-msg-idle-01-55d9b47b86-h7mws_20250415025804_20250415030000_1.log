2025-04-15 02:00:02,218943000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	176	[7deb0e3b-1ff2-4d82-b456-2fb3992c8939][SecretStore#121][Communicator#1000:SecretStore#207:2219:2219:2218] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-h7mws] key is already updated
2025-04-15 02:00:02,219143000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	176	[7deb0e3b-1ff2-4d82-b456-2fb3992c8939][SecretStore#122][Communicator#1000:SecretStore#121:2220:2220:1] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-h7mws] update key fin
2025-04-15 02:00:02,219233000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	176	[7deb0e3b-1ff2-4d82-b456-2fb3992c8939][Communicator#1007][Communicator#1000:SecretStore#122:2220:2220:0] [updateEncryptionKeyMsg api-msg-idle-01-55d9b47b86-h7mws] execute fin
2025-04-15 02:10:02,175185000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	182	[70e240dd-a746-4c55-b69b-dc73d449a41b][Communicator#1000][1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:10:02,175651000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	182	[70e240dd-a746-4c55-b69b-dc73d449a41b][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:10:02,176041000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	182	[70e240dd-a746-4c55-b69b-dc73d449a41b][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:10:02,203928000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	182	[70e240dd-a746-4c55-b69b-dc73d449a41b][Communicator#1003][Communicator#1000:Communicator#1002:29:28:28] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:10:02,204179000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	182	[70e240dd-a746-4c55-b69b-dc73d449a41b][Communicator#1023][Communicator#1000:Communicator#1003:30:29:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:10:02,204695000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	182	[70e240dd-a746-4c55-b69b-dc73d449a41b][Communicator#1006][Communicator#1000:Communicator#1023:30:29:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:15:03,263129000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	187	[db72b42a-67f6-47d9-a095-b180cc40767e][Communicator#1000][1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:15:03,263456000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	187	[db72b42a-67f6-47d9-a095-b180cc40767e][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:15:03,263881000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	187	[db72b42a-67f6-47d9-a095-b180cc40767e][Communicator#1002][Communicator#1000:Communicator#1001:1:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:15:03,292982000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	187	[db72b42a-67f6-47d9-a095-b180cc40767e][Communicator#1003][Communicator#1000:Communicator#1002:30:29:29] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:15:03,293223000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	187	[db72b42a-67f6-47d9-a095-b180cc40767e][Communicator#1023][Communicator#1000:Communicator#1003:31:30:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:15:03,293581000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	187	[db72b42a-67f6-47d9-a095-b180cc40767e][Communicator#1006][Communicator#1000:Communicator#1023:31:30:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:17:02,231903000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	192	[3cafdabe-17bd-448d-b9a4-59df43ce0aa2][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:17:02,232200000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	192	[3cafdabe-17bd-448d-b9a4-59df43ce0aa2][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:17:02,232605000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	192	[3cafdabe-17bd-448d-b9a4-59df43ce0aa2][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:17:02,263361000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	192	[3cafdabe-17bd-448d-b9a4-59df43ce0aa2][Communicator#1003][Communicator#1000:Communicator#1002:32:32:31] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:17:02,263651000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	192	[3cafdabe-17bd-448d-b9a4-59df43ce0aa2][Communicator#1023][Communicator#1000:Communicator#1003:32:32:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:17:02,264064000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	192	[3cafdabe-17bd-448d-b9a4-59df43ce0aa2][Communicator#1006][Communicator#1000:Communicator#1023:33:33:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:25:02,477401000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	197	[cabe6ea4-55e7-4a19-a856-3993bd336fdd][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:25:02,477735000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	197	[cabe6ea4-55e7-4a19-a856-3993bd336fdd][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:25:02,478180000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	197	[cabe6ea4-55e7-4a19-a856-3993bd336fdd][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:25:02,505691000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	197	[cabe6ea4-55e7-4a19-a856-3993bd336fdd][Communicator#1003][Communicator#1000:Communicator#1002:28:28:27] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:25:02,505924000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	197	[cabe6ea4-55e7-4a19-a856-3993bd336fdd][Communicator#1023][Communicator#1000:Communicator#1003:28:28:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:25:02,506287000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	197	[cabe6ea4-55e7-4a19-a856-3993bd336fdd][Communicator#1006][Communicator#1000:Communicator#1023:29:29:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:30:00,003792000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	202	[c24cefa8-cc01-4d80-b95e-c5565a3164ef][Communicator#1000][0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-h7mws] SaveKeyCommunicator str
2025-04-15 02:30:00,004149000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	202	[c24cefa8-cc01-4d80-b95e-c5565a3164ef][SecretStore#110][Communicator#1000:Communicator#1000:1:1:1] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-h7mws] save next key str
2025-04-15 02:30:00,004299000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	202	[c24cefa8-cc01-4d80-b95e-c5565a3164ef][SecretStore#112][Communicator#1000:SecretStore#110:1:1:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-h7mws] next key is already exists
2025-04-15 02:30:00,004421000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	202	[c24cefa8-cc01-4d80-b95e-c5565a3164ef][SecretStore#113][Communicator#1000:SecretStore#112:1:1:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-h7mws] save next key fin
2025-04-15 02:30:00,004508000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	202	[c24cefa8-cc01-4d80-b95e-c5565a3164ef][Communicator#1007][Communicator#1000:SecretStore#113:1:1:0] [saveEncryptionKeyMsg api-msg-idle-01-55d9b47b86-h7mws] execute fin
2025-04-15 02:35:59,024822000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][Communicator#1000][0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] AppCommunicator str
2025-04-15 02:35:59,026079000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][Communicator#1001][Communicator#1000:Communicator#1000:2:2:2] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:35:59,026664000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][SecretStore#100][Communicator#1000:Communicator#1001:2:2:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] get key str
2025-04-15 02:35:59,026910000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][SecretStore#101][Communicator#1000:SecretStore#100:2:2:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] get key list from local file
2025-04-15 02:35:59,027163000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][SecretStore#102][Communicator#1000:SecretStore#101:3:3:1] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] get key fin
2025-04-15 02:35:59,536131000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][Communicator#1002][Communicator#1000:SecretStore#102:512:512:509] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:35:59,539026000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][SecretStore#100][Communicator#1000:Communicator#1002:514:514:2] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] get key str
2025-04-15 02:35:59,539182000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][SecretStore#101][Communicator#1000:SecretStore#100:515:515:1] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] get key list from local file
2025-04-15 02:35:59,539386000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][SecretStore#102][Communicator#1000:SecretStore#101:515:515:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] get key fin
2025-04-15 02:35:59,613551000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][Communicator#1003][Communicator#1000:SecretStore#102:589:589:74] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:35:59,946033000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][Communicator#1004][Communicator#1000:Communicator#1003:921:921:332] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] sendMessage fin
2025-04-15 02:35:59,953477000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][ConnectionWithSqlDb#1001][Communicator#1000:Communicator#1004:929:929:8] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] db connection create str
2025-04-15 02:35:59,968428000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][ConnectionWithSqlDb#1003][Communicator#1000:ConnectionWithSqlDb#1001:944:944:15] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] db token create str
2025-04-15 02:36:00,370302000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][ConnectionWithSqlDb#1004][Communicator#1000:ConnectionWithSqlDb#1003:1346:1346:402] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] db token create fin
2025-04-15 02:36:00,510244000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][ConnectionWithSqlDb#1002][Communicator#1000:ConnectionWithSqlDb#1004:1486:1486:140] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] db connection create fin
2025-04-15 02:36:00,567556000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][SecretStore#100][Communicator#1000:ConnectionWithSqlDb#1002:1543:1543:57] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] get key str
2025-04-15 02:36:00,567711000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][SecretStore#101][Communicator#1000:SecretStore#100:1543:1543:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] get key list from local file
2025-04-15 02:36:00,567896000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][SecretStore#102][Communicator#1000:SecretStore#101:1543:1543:0] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] get key fin
2025-04-15 02:36:00,568496000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[40b57a54-f16d-4837-874e-4fc6b6607c51][Communicator#1006][Communicator#1000:SecretStore#102:1544:1544:1] [190_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:36:02,349835000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[f174e495-8856-42a2-97a7-82cb79585c2c][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:36:02,350048000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[f174e495-8856-42a2-97a7-82cb79585c2c][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:36:02,350431000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[f174e495-8856-42a2-97a7-82cb79585c2c][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:36:02,361611000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[f174e495-8856-42a2-97a7-82cb79585c2c][Communicator#1003][Communicator#1000:Communicator#1002:12:12:11] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:36:02,361821000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[f174e495-8856-42a2-97a7-82cb79585c2c][Communicator#1023][Communicator#1000:Communicator#1003:12:12:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:36:02,362189000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[f174e495-8856-42a2-97a7-82cb79585c2c][Communicator#1006][Communicator#1000:Communicator#1023:13:13:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:36:20,646111000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[179ee428-fe0e-452a-97e7-c8368ce8aef7][Communicator#1000][1] [100_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:36:20,646757000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[179ee428-fe0e-452a-97e7-c8368ce8aef7][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [100_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:36:20,647234000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[179ee428-fe0e-452a-97e7-c8368ce8aef7][Communicator#1002][Communicator#1000:Communicator#1001:2:1:1] [100_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:36:20,664125000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[179ee428-fe0e-452a-97e7-c8368ce8aef7][Communicator#1003][Communicator#1000:Communicator#1002:19:18:17] [100_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:36:24,239631000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[179ee428-fe0e-452a-97e7-c8368ce8aef7][Communicator#1004][Communicator#1000:Communicator#1003:3594:3593:3575] [100_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] sendMessage fin
2025-04-15 02:36:24,239975000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	204	[179ee428-fe0e-452a-97e7-c8368ce8aef7][Communicator#1006][Communicator#1000:Communicator#1004:3594:3593:0] [100_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:43:02,382031000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[713ec004-939d-4722-9ff4-24823e536efd][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:43:02,382324000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[713ec004-939d-4722-9ff4-24823e536efd][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:43:02,382654000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[713ec004-939d-4722-9ff4-24823e536efd][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:43:02,409589000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[713ec004-939d-4722-9ff4-24823e536efd][Communicator#1003][Communicator#1000:Communicator#1002:28:28:27] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:43:02,409824000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[713ec004-939d-4722-9ff4-24823e536efd][Communicator#1023][Communicator#1000:Communicator#1003:28:28:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:43:02,410177000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[713ec004-939d-4722-9ff4-24823e536efd][Communicator#1006][Communicator#1000:Communicator#1023:29:29:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:43:02,509065000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[da2bb13d-cc4d-496b-8f62-fc5b99150c57][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:43:02,509254000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[da2bb13d-cc4d-496b-8f62-fc5b99150c57][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:43:02,510171000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[da2bb13d-cc4d-496b-8f62-fc5b99150c57][Communicator#1002][Communicator#1000:Communicator#1001:2:2:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:43:02,522496000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[da2bb13d-cc4d-496b-8f62-fc5b99150c57][Communicator#1003][Communicator#1000:Communicator#1002:14:14:12] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:43:02,522680000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[da2bb13d-cc4d-496b-8f62-fc5b99150c57][Communicator#1023][Communicator#1000:Communicator#1003:14:14:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:43:02,522895000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[da2bb13d-cc4d-496b-8f62-fc5b99150c57][Communicator#1006][Communicator#1000:Communicator#1023:14:14:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:44:02,474865000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[e2aaba4c-5754-40ba-b2e7-eec61cd4ea09][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:44:02,475058000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[e2aaba4c-5754-40ba-b2e7-eec61cd4ea09][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:44:02,475423000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[e2aaba4c-5754-40ba-b2e7-eec61cd4ea09][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:44:02,498428000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[e2aaba4c-5754-40ba-b2e7-eec61cd4ea09][Communicator#1003][Communicator#1000:Communicator#1002:24:24:23] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:44:02,498668000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[e2aaba4c-5754-40ba-b2e7-eec61cd4ea09][Communicator#1023][Communicator#1000:Communicator#1003:24:24:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:44:02,498919000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	228	[e2aaba4c-5754-40ba-b2e7-eec61cd4ea09][Communicator#1006][Communicator#1000:Communicator#1023:24:24:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:46:02,490530000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	236	[3adf3d68-c4f7-4534-9c16-9ee75bdc0762][Communicator#1000][0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:46:02,490843000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	236	[3adf3d68-c4f7-4534-9c16-9ee75bdc0762][Communicator#1001][Communicator#1000:Communicator#1000:0:0:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:46:02,491499000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	236	[3adf3d68-c4f7-4534-9c16-9ee75bdc0762][Communicator#1002][Communicator#1000:Communicator#1001:1:1:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:46:02,518500000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	236	[3adf3d68-c4f7-4534-9c16-9ee75bdc0762][Communicator#1003][Communicator#1000:Communicator#1002:28:28:27] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:46:02,518706000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	236	[3adf3d68-c4f7-4534-9c16-9ee75bdc0762][Communicator#1023][Communicator#1000:Communicator#1003:28:28:0] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:46:02,519067000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	236	[3adf3d68-c4f7-4534-9c16-9ee75bdc0762][Communicator#1006][Communicator#1000:Communicator#1023:29:29:1] [20_2_1:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:47:33,488093000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	241	[f814d50f-514e-4651-91d3-d818d5da0b7e][Communicator#1000][2] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:47:33,488443000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	241	[f814d50f-514e-4651-91d3-d818d5da0b7e][Communicator#1001][Communicator#1000:Communicator#1000:2:0:0] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:47:33,489213000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	241	[f814d50f-514e-4651-91d3-d818d5da0b7e][Communicator#1002][Communicator#1000:Communicator#1001:3:1:1] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:47:33,520784000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	241	[f814d50f-514e-4651-91d3-d818d5da0b7e][Communicator#1003][Communicator#1000:Communicator#1002:34:32:31] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:47:33,521000000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	241	[f814d50f-514e-4651-91d3-d818d5da0b7e][Communicator#1023][Communicator#1000:Communicator#1003:34:32:0] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:47:33,521330000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	241	[f814d50f-514e-4651-91d3-d818d5da0b7e][Communicator#1006][Communicator#1000:Communicator#1023:35:33:1] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:50:04,943070000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	246	[4dd14912-f214-431c-988c-40cd8aa9d6f4][Communicator#1000][1] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:50:04,943373000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	246	[4dd14912-f214-431c-988c-40cd8aa9d6f4][Communicator#1001][Communicator#1000:Communicator#1000:1:0:0] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:50:04,944074000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	246	[4dd14912-f214-431c-988c-40cd8aa9d6f4][Communicator#1002][Communicator#1000:Communicator#1001:2:1:1] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:50:04,973037000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	246	[4dd14912-f214-431c-988c-40cd8aa9d6f4][Communicator#1003][Communicator#1000:Communicator#1002:30:29:28] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:50:04,973223000	W	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	246	[4dd14912-f214-431c-988c-40cd8aa9d6f4][Communicator#1023][Communicator#1000:Communicator#1003:31:30:1] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] CustomerClient Data Error fin
2025-04-15 02:50:04,973540000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	246	[4dd14912-f214-431c-988c-40cd8aa9d6f4][Communicator#1006][Communicator#1000:Communicator#1023:31:30:0] [70_2_2:24:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
2025-04-15 02:58:04,734049000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	251	[520edfdb-9efc-4224-804c-f2143bd06c45][Communicator#1000][0] [70_2_2:20:* api-msg-idle-01-55d9b47b86-h7mws] TscCommunicator str
2025-04-15 02:58:04,734336000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	251	[520edfdb-9efc-4224-804c-f2143bd06c45][Communicator#1001][Communicator#1000:Communicator#1000:1:1:1] [70_2_2:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest str
2025-04-15 02:58:04,734823000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	251	[520edfdb-9efc-4224-804c-f2143bd06c45][Communicator#1002][Communicator#1000:Communicator#1001:1:1:0] [70_2_2:20:* api-msg-idle-01-55d9b47b86-h7mws] authenticate fin
2025-04-15 02:58:04,764307000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	251	[520edfdb-9efc-4224-804c-f2143bd06c45][Communicator#1003][Communicator#1000:Communicator#1002:31:31:30] [70_2_2:20:* api-msg-idle-01-55d9b47b86-h7mws] accessCustomerClient fin
2025-04-15 02:58:04,788342000	F	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	251	[520edfdb-9efc-4224-804c-f2143bd06c45][Communicator#1026][Communicator#1000:Communicator#1003:55:55:24] [70_2_2:20:* api-msg-idle-01-55d9b47b86-h7mws] sendMessage error fin
2025-04-15 02:58:04,788979000	F	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	251	[520edfdb-9efc-4224-804c-f2143bd06c45][Communicator#999][Communicator#1000:Communicator#1026:55:55:0] io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:271)
	io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:252)
	io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:165)
	mazda.tk2.api.grpc.message.MessengerGrpc$MessengerBlockingStub.communicate(MessengerGrpc.java:157)
	mazda.tk2.api.common.imp.ctl.MessagingFoundationSender.sendMessage(MessagingFoundationSender.java:45)
	mazda.tk2.api.common.imp.ctl.TscCommunicator.invokeRequest(TscCommunicator.java:254)
	messagereceiver.RemoteConfirmNotification.run(RemoteConfirmNotification.java:44)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-15 02:58:04,789354000	I	api-msg-idle-01-55d9b47b86-h7mws	api-msg	23	251	[520edfdb-9efc-4224-804c-f2143bd06c45][Communicator#1006][Communicator#1000:Communicator#?:56:56:1] [70_2_2:20:* api-msg-idle-01-55d9b47b86-h7mws] invokeRequest fin
