2025-04-11 02:05:53,590075000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleFunction#101][24] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 02:05:53,590870000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:24:0:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"MAZDATR1000000555"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744337153566","x-apiid":"70_2_2:24:*","x-isnotifiable":"1","x-transactionid":"36626ac1-6909-45cd-9597-a9790a524aae"}}
2025-04-11 02:05:53,591012000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:24:0:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 02:05:53,591260000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:25:1:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 02:05:53,591336000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:25:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 02:05:53,591396000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:25:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 02:05:53,591442000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:25:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 02:05:53,591506000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:25:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 02:05:53,591577000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:25:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 02:05:54,410964000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:844:820:819] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 02:05:54,471169000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:905:881:61] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 02:05:54,474600000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:908:884:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 02:05:54,474895000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:908:884:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 02:05:54,475979000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:909:885:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 02:05:54,476064000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:910:886:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 02:05:54,476308000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	108	[36626ac1-6909-45cd-9597-a9790a524aae][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:910:886:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 02:13:26,497406000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleFunction#101][35] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 02:13:26,498171000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:36:1:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"FSTEST00000000001"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744337606462","x-apiid":"70_2_2:24:*","x-isnotifiable":"1","x-transactionid":"d6d692ed-a727-44d0-ba28-d88e30ec9ab0"}}
2025-04-11 02:13:26,498312000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:36:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 02:13:26,498582000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:36:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 02:13:26,498667000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:36:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 02:13:26,498728000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:36:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 02:13:26,498797000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:36:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 02:13:26,498870000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:36:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 02:13:26,498960000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:36:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 02:13:26,889344000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:427:392:391] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 02:13:26,948702000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:486:451:59] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 02:13:26,952203000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:490:455:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 02:13:26,952529000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:490:455:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 02:13:26,953625000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:491:456:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 02:13:26,953716000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:491:456:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 02:13:26,953939000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	113	[d6d692ed-a727-44d0-ba28-d88e30ec9ab0][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:491:456:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 02:51:53,920451000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleFunction#101][19] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 02:51:53,921723000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:20:1:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"MAZDATR1000000555"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744339913901","x-apiid":"70_2_2:24:*","x-isnotifiable":"1","x-transactionid":"393918a2-3920-4afb-af20-400dfd9a56ad"}}
2025-04-11 02:51:53,921882000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:20:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 02:51:53,922134000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:21:2:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 02:51:53,922222000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:21:2:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 02:51:53,922280000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:21:2:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 02:51:53,922336000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:21:2:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 02:51:53,922417000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:21:2:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 02:51:53,922504000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:21:2:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 02:51:54,357129000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:455:436:434] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 02:51:54,417054000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:515:496:60] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 02:51:54,420559000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:519:500:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 02:51:54,420842000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:519:500:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 02:51:54,421876000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:520:501:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 02:51:54,421967000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:520:501:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 02:51:54,422208000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	118	[393918a2-3920-4afb-af20-400dfd9a56ad][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:521:502:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
