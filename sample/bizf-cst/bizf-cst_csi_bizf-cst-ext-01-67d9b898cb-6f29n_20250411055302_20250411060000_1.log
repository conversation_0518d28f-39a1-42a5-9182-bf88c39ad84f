2025-04-11 05:18:01,951946000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleFunction#101][24] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 05:18:01,952483000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:25:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744348681927","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"7b1177ad-ea63-4369-860b-cbbb0bdbe6cd"}}
2025-04-11 05:18:01,952577000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 05:18:01,952745000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 05:18:01,952789000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 05:18:01,952818000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 05:18:01,952846000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 05:18:01,956469000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:29:5:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 05:18:01,956714000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:29:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 05:18:01,957636000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:30:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 05:18:01,957695000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:30:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 05:18:01,957847000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	180	[7b1177ad-ea63-4369-860b-cbbb0bdbe6cd][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:30:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 05:19:02,450230000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleFunction#101][24] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 05:19:02,451118000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:25:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744348742426","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"0214d83d-c9bc-4169-b439-1d877ca725fa"}}
2025-04-11 05:19:02,451233000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 05:19:02,451412000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 05:19:02,451469000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 05:19:02,451507000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 05:19:02,451544000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:25:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 05:19:02,454803000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:28:4:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 05:19:02,455047000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:28:4:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 05:19:02,455851000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:29:5:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 05:19:02,455908000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:29:5:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 05:19:02,456062000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	182	[0214d83d-c9bc-4169-b439-1d877ca725fa][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:30:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 05:26:02,110670000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleFunction#101][18] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 05:26:02,111553000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:19:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744349162092","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"d7149079-cc9d-4b5a-9eac-f45e1c3309d3"}}
2025-04-11 05:26:02,111713000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 05:26:02,111892000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 05:26:02,111945000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 05:26:02,111983000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 05:26:02,112019000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:20:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 05:26:02,115773000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:23:5:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 05:26:02,116118000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:23:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 05:26:02,117230000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:25:7:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 05:26:02,117333000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:25:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 05:26:02,117529000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	184	[d7149079-cc9d-4b5a-9eac-f45e1c3309d3][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:25:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 05:34:02,678665000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleFunction#101][19] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 05:34:02,679143000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:20:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744349642659","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"72cfac06-b5ae-4ba5-8178-c995eb019d21"}}
2025-04-11 05:34:02,679234000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 05:34:02,679364000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 05:34:02,679400000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 05:34:02,679425000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 05:34:02,679449000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 05:34:02,683048000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:24:5:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 05:34:02,683250000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:24:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 05:34:02,684035000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:25:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 05:34:02,684088000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:25:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 05:34:02,684296000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	186	[72cfac06-b5ae-4ba5-8178-c995eb019d21][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:25:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 05:42:03,067695000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleFunction#101][25] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 05:42:03,068815000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:26:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744350123042","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"07c6e068-b443-4553-b8c5-2c678208e8e6"}}
2025-04-11 05:42:03,068974000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:26:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 05:42:03,069180000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:27:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 05:42:03,069255000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:27:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 05:42:03,069308000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:27:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 05:42:03,069349000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:27:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 05:42:03,072892000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:30:5:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 05:42:03,073097000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:31:6:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 05:42:03,074241000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:32:7:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 05:42:03,074329000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:32:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 05:42:03,074535000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[07c6e068-b443-4553-b8c5-2c678208e8e6][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:32:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 05:42:54,118706000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleFunction#101][15] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 05:42:54,119412000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:16:1:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"FSTEST00000000002"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744350174103","x-apiid":"70_2_2:24:*","x-isnotifiable":"1","x-transactionid":"02e69595-26c5-4727-bb6c-95161c263e99"}}
2025-04-11 05:42:54,119594000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:16:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 05:42:54,119812000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:16:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 05:42:54,119893000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:16:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 05:42:54,119947000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:16:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 05:42:54,120000000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:16:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 05:42:54,123368000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:20:5:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 05:42:54,123504000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:20:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 05:42:54,124631000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:21:6:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 05:42:54,124714000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:21:6:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 05:42:54,124916000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[02e69595-26c5-4727-bb6c-95161c263e99][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:21:6:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 05:43:23,295738000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleFunction#101][32] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 05:43:23,296280000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:33:1:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"FSTEST00000000002"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744350203263","x-apiid":"70_2_2:24:*","x-isnotifiable":"1","x-transactionid":"4e16dc02-dc8c-4f18-9806-d88ffd9b538b"}}
2025-04-11 05:43:23,296469000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 05:43:23,296744000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 05:43:23,296842000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 05:43:23,296923000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 05:43:23,296978000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 05:43:23,302649000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:39:7:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 05:43:23,302789000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:39:7:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 05:43:23,303651000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:40:8:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 05:43:23,303761000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:40:8:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 05:43:23,303987000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	188	[4e16dc02-dc8c-4f18-9806-d88ffd9b538b][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:40:8:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 05:53:02,000493000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleFunction#101][18] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 05:53:02,001217000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:19:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744350781982","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"b04a0f8e-64e9-4fda-b857-fa1eac0ad00b"}}
2025-04-11 05:53:02,001357000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 05:53:02,001506000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 05:53:02,001547000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 05:53:02,001573000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 05:53:02,001598000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 05:53:02,005359000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:23:5:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 05:53:02,005628000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:23:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 05:53:02,006617000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:24:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 05:53:02,006671000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:24:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 05:53:02,006856000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	190	[b04a0f8e-64e9-4fda-b857-fa1eac0ad00b][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:24:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
