2025-04-11 06:21:56,615338000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleFunction#101][15] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 06:21:56,615869000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:15:0:0] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PAS0000PAS0000011"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744352516600","x-apiid":"100_2_1:24:*","x-isnotifiable":"1","x-transactionid":"0b9e36c2-b121-4c50-a84a-e5a65de4c9a5"}}
2025-04-11 06:21:56,615953000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:15:0:0] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 06:21:56,616098000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:16:1:1] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 06:21:56,616139000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:16:1:0] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 06:21:56,616166000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:16:1:0] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 06:21:56,616193000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:16:1:0] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 06:21:56,620193000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:20:5:1] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 06:21:56,620403000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:20:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 06:21:56,621174000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:21:6:1] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 06:21:56,621219000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:21:6:0] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 06:21:56,621341000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	203	[0b9e36c2-b121-4c50-a84a-e5a65de4c9a5][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:21:6:0] [100_2_1:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 06:23:34,044481000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoFunction#101][37] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 06:23:34,045071000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoFunction#103][UserInfoFunction#101:UserInfoFunction#101:38:1:1] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"idmId":"db8b8ab0-f9c1-489a-9ba8-d12266fa6446"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744352614007","x-apiid":"uploadUsherId","x-devicesessionid":"36304359-29f9-4719-a7a6-982498061843","x-transactionid":"08eb54c0-d55d-43ec-877a-de32831e7fcf"}}
2025-04-11 06:23:34,047529000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoApplication#101][UserInfoFunction#101:UserInfoFunction#103:40:3:2] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 06:23:34,047712000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoApplication#111][UserInfoFunction#101:UserInfoApplication#101:40:3:0] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 06:23:34,047784000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoApplication#113][UserInfoFunction#101:UserInfoApplication#111:40:3:0] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 06:23:34,047814000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoApplication#112][UserInfoFunction#101:UserInfoApplication#113:40:3:0] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 06:23:34,047841000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoApplication#121][UserInfoFunction#101:UserInfoApplication#112:40:3:0] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 06:23:34,086178000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoApplication#123][UserInfoFunction#101:UserTableManager#102:79:42:1] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #5-1 performInner result= true
2025-04-11 06:23:34,086265000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoApplication#122][UserInfoFunction#101:UserInfoApplication#123:79:42:0] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner fin
2025-04-11 06:23:34,086305000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoApplication#102][UserInfoFunction#101:UserInfoApplication#122:79:42:0] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform fin
2025-04-11 06:23:34,087461000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoApplication#131][UserInfoFunction#101:UserInfoApplication#102:80:43:1] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 06:23:34,087519000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoFunction#104][UserInfoFunction#101:UserInfoApplication#131:80:43:0] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"firstName":"UserAN","lastName":"TestAN","country":null,"formatInfo":"00000000","rucId":"f4039667-37c8-4a0f-9571-1c503c45d3c2","mailAddress":"<EMAIL>","rccId":"f4039667-37c8-4a0f-9571-1c503c45d3c2","tel":"+819036545089","middleName":"User","locale":"en-DE","internalUserId":210000063}}
2025-04-11 06:23:34,087713000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[08eb54c0-d55d-43ec-877a-de32831e7fcf][UserInfoFunction#102][UserInfoFunction#101:UserInfoFunction#104:80:43:0] [uploadUsherId bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 06:23:35,925651000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoFunction#101][6] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 06:23:35,926051000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoFunction#103][UserInfoFunction#101:UserInfoFunction#101:7:1:1] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"idmId":"db8b8ab0-f9c1-489a-9ba8-d12266fa6446"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744352615919","x-apiid":"getMergeScore","x-devicesessionid":"36304359-29f9-4719-a7a6-982498061843","x-transactionid":"cc468f8c-c853-467e-968d-ccdc7b00e729"}}
2025-04-11 06:23:35,926144000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoApplication#101][UserInfoFunction#101:UserInfoFunction#103:7:1:0] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 06:23:35,926287000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoApplication#111][UserInfoFunction#101:UserInfoApplication#101:7:1:0] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 06:23:35,926327000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoApplication#113][UserInfoFunction#101:UserInfoApplication#111:7:1:0] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 06:23:35,926353000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoApplication#112][UserInfoFunction#101:UserInfoApplication#113:7:1:0] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 06:23:35,926398000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoApplication#121][UserInfoFunction#101:UserInfoApplication#112:7:1:0] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 06:23:35,944164000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoApplication#123][UserInfoFunction#101:UserTableManager#102:25:19:1] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #5-1 performInner result= true
2025-04-11 06:23:35,944307000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoApplication#122][UserInfoFunction#101:UserInfoApplication#123:25:19:0] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner fin
2025-04-11 06:23:35,944340000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoApplication#102][UserInfoFunction#101:UserInfoApplication#122:25:19:0] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform fin
2025-04-11 06:23:35,945210000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoApplication#131][UserInfoFunction#101:UserInfoApplication#102:26:20:1] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 06:23:35,945251000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoFunction#104][UserInfoFunction#101:UserInfoApplication#131:26:20:0] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"firstName":"UserAN","lastName":"TestAN","country":null,"formatInfo":"00000000","rucId":"f4039667-37c8-4a0f-9571-1c503c45d3c2","mailAddress":"<EMAIL>","rccId":"f4039667-37c8-4a0f-9571-1c503c45d3c2","tel":"+819036545089","middleName":"User","locale":"en-DE","internalUserId":210000063}}
2025-04-11 06:23:35,945438000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	205	[cc468f8c-c853-467e-968d-ccdc7b00e729][UserInfoFunction#102][UserInfoFunction#101:UserInfoFunction#104:26:20:0] [getMergeScore bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 06:24:59,352318000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoFunction#101][27] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 06:24:59,352822000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoFunction#103][UserInfoFunction#101:UserInfoFunction#101:27:0:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"idmId":"db8b8ab0-f9c1-489a-9ba8-d12266fa6446"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744352699325","x-apiid":"getUserByAccessToken","x-devicesessionid":"36304359-29f9-4719-a7a6-982498061843","x-transactionid":"4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf"}}
2025-04-11 06:24:59,352909000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoApplication#101][UserInfoFunction#101:UserInfoFunction#103:27:0:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 06:24:59,353064000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoApplication#111][UserInfoFunction#101:UserInfoApplication#101:28:1:1] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 06:24:59,353101000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoApplication#113][UserInfoFunction#101:UserInfoApplication#111:28:1:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 06:24:59,353125000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoApplication#112][UserInfoFunction#101:UserInfoApplication#113:28:1:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 06:24:59,353147000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoApplication#121][UserInfoFunction#101:UserInfoApplication#112:28:1:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 06:24:59,384898000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoApplication#123][UserInfoFunction#101:UserTableManager#102:59:32:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #5-1 performInner result= true
2025-04-11 06:24:59,384945000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoApplication#122][UserInfoFunction#101:UserInfoApplication#123:59:32:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner fin
2025-04-11 06:24:59,384967000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoApplication#102][UserInfoFunction#101:UserInfoApplication#122:59:32:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform fin
2025-04-11 06:24:59,385640000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoApplication#131][UserInfoFunction#101:UserInfoApplication#102:60:33:1] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 06:24:59,385677000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoFunction#104][UserInfoFunction#101:UserInfoApplication#131:60:33:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"firstName":"UserAN","lastName":"TestAN","country":"DE","formatInfo":"00000000","rucId":"f4039667-37c8-4a0f-9571-1c503c45d3c2","mailAddress":"<EMAIL>","rccId":"f4039667-37c8-4a0f-9571-1c503c45d3c2","tel":"+819036545089","middleName":"User","locale":"en-DE","internalUserId":210000063}}
2025-04-11 06:24:59,385830000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[4b028b34-6b5a-4a6c-b73e-62a1d9fed9cf][UserInfoFunction#102][UserInfoFunction#101:UserInfoFunction#104:60:33:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 06:25:00,843444000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoFunction#101][4] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 06:25:00,843811000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoFunction#103][UserInfoFunction#101:UserInfoFunction#101:4:0:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"idmId":"db8b8ab0-f9c1-489a-9ba8-d12266fa6446"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744352700839","x-apiid":"getUserByAccessToken","x-devicesessionid":"36304359-29f9-4719-a7a6-982498061843","x-transactionid":"3ad5a517-093d-48ba-a952-adb65dc3eb14"}}
2025-04-11 06:25:00,843896000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoApplication#101][UserInfoFunction#101:UserInfoFunction#103:4:0:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 06:25:00,844031000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoApplication#111][UserInfoFunction#101:UserInfoApplication#101:5:1:1] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 06:25:00,844066000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoApplication#113][UserInfoFunction#101:UserInfoApplication#111:5:1:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 06:25:00,844089000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoApplication#112][UserInfoFunction#101:UserInfoApplication#113:5:1:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 06:25:00,844111000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoApplication#121][UserInfoFunction#101:UserInfoApplication#112:5:1:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 06:25:00,861014000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoApplication#123][UserInfoFunction#101:UserTableManager#102:21:17:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #5-1 performInner result= true
2025-04-11 06:25:00,861067000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoApplication#122][UserInfoFunction#101:UserInfoApplication#123:22:18:1] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner fin
2025-04-11 06:25:00,861096000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoApplication#102][UserInfoFunction#101:UserInfoApplication#122:22:18:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform fin
2025-04-11 06:25:00,861897000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoApplication#131][UserInfoFunction#101:UserInfoApplication#102:22:18:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 06:25:00,861941000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoFunction#104][UserInfoFunction#101:UserInfoApplication#131:22:18:0] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OK","resultCode":"CST200000","details":[{"detailCode":"200000","reason":"OK"}],"data":{"firstName":"UserAN","lastName":"TestAN","country":"DE","formatInfo":"00000000","rucId":"f4039667-37c8-4a0f-9571-1c503c45d3c2","mailAddress":"<EMAIL>","rccId":"f4039667-37c8-4a0f-9571-1c503c45d3c2","tel":"+819036545089","middleName":"User","locale":"en-DE","internalUserId":210000063}}
2025-04-11 06:25:00,862103000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	213	[3ad5a517-093d-48ba-a952-adb65dc3eb14][UserInfoFunction#102][UserInfoFunction#101:UserInfoFunction#104:23:19:1] [getUserByAccessToken bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 06:38:02,021706000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleFunction#101][20] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 06:38:02,022192000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:21:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744353482001","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"1410180e-ecab-4532-aab1-7e3800c80290"}}
2025-04-11 06:38:02,022254000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 06:38:02,022397000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 06:38:02,022431000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 06:38:02,022453000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 06:38:02,022488000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 06:38:02,026346000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:25:5:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 06:38:02,026595000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:25:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 06:38:02,027472000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:26:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 06:38:02,027517000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:26:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 06:38:02,027659000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	222	[1410180e-ecab-4532-aab1-7e3800c80290][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:26:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 06:40:38,113953000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleFunction#101][26] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 06:40:38,114457000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:27:1:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"MAZDATR1000000333"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744353638087","x-apiid":"70_2_2:24:*","x-isnotifiable":"1","x-transactionid":"5b6c3469-a9ad-41a0-a304-371c5b781f13"}}
2025-04-11 06:40:38,114523000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:27:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 06:40:38,114655000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:27:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 06:40:38,114689000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:27:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 06:40:38,114710000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:27:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 06:40:38,114733000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:27:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 06:40:38,118563000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:31:5:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 06:40:38,118805000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:31:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 06:40:38,119657000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:32:6:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 06:40:38,119704000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:32:6:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 06:40:38,119869000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	224	[5b6c3469-a9ad-41a0-a304-371c5b781f13][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:32:6:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 06:57:22,731996000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleFunction#101][23] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 06:57:22,732453000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:24:1:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"FSTEST00000000002"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744354642708","x-apiid":"70_2_2:24:*","x-isnotifiable":"1","x-transactionid":"719bb776-09ce-4756-ba19-11d6c7f6df7d"}}
2025-04-11 06:57:22,732516000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:24:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 06:57:22,732699000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:24:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 06:57:22,732760000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:24:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 06:57:22,732783000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:24:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 06:57:22,732806000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:24:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 06:57:22,736827000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:28:5:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 06:57:22,737082000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:28:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 06:57:22,737956000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:29:6:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 06:57:22,738000000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:29:6:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 06:57:22,738160000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	226	[719bb776-09ce-4756-ba19-11d6c7f6df7d][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:30:7:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
