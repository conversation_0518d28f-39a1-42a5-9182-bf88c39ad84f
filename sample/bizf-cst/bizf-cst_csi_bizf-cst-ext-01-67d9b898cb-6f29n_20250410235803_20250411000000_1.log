2025-04-10 23:17:08,980282000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleFunction#101][65472] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-10 23:17:09,038099000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:65537:65:65] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744326963500","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"93672356-a8cf-4c93-afcb-67823dff1e4d"}}
2025-04-10 23:17:09,041404000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:65541:69:4] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-10 23:17:09,083014000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:65582:110:41] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-10 23:17:09,083922000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:65583:111:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-10 23:17:09,084072000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:65584:112:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-10 23:17:09,084159000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:65584:112:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-10 23:17:09,085703000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:65585:113:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-10 23:17:09,107109000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:65607:135:22] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-10 23:17:10,584243000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:67084:1612:1477] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-10 23:17:10,726921000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:67226:1754:142] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-10 23:17:10,776237000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:67276:1804:3] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-10 23:17:10,776988000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:67276:1804:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-10 23:17:10,786795000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:67286:1814:10] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-10 23:17:10,786952000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:67286:1814:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-10 23:17:10,787833000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	17	[93672356-a8cf-4c93-afcb-67823dff1e4d][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:67287:1815:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-10 23:31:02,489601000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleFunction#101][222] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-10 23:31:02,490689000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:223:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744327862267","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"6c87835c-f04e-474f-95b2-1801f3d082bf"}}
2025-04-10 23:31:02,490870000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:223:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-10 23:31:02,491202000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:224:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-10 23:31:02,491323000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:224:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-10 23:31:02,491419000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:224:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-10 23:31:02,491511000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:224:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-10 23:31:02,491613000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:224:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-10 23:31:02,491747000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:224:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-10 23:31:02,895038000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:627:405:403] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-10 23:31:02,959435000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:692:470:65] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-10 23:31:02,964056000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:696:474:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-10 23:31:02,964615000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:697:475:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-10 23:31:02,965953000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:698:476:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-10 23:31:02,966063000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:699:477:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-10 23:31:02,966330000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	52	[6c87835c-f04e-474f-95b2-1801f3d082bf][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:699:477:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-10 23:44:01,970390000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleFunction#101][29] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-10 23:44:01,971114000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:30:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744328641941","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"187ca9f3-e50e-45d0-af2f-70f78437f942"}}
2025-04-10 23:44:01,971253000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-10 23:44:01,971505000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-10 23:44:01,971591000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-10 23:44:01,971657000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-10 23:44:01,971739000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-10 23:44:01,971825000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-10 23:44:01,971929000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-10 23:44:02,908484000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:967:938:937] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-10 23:44:02,967178000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:1026:997:59] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-10 23:44:02,970903000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:1029:1000:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-10 23:44:02,971284000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:1030:1001:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-10 23:44:02,972584000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:1031:1002:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-10 23:44:02,972742000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:1031:1002:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-10 23:44:02,973027000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	58	[187ca9f3-e50e-45d0-af2f-70f78437f942][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:1031:1002:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-10 23:47:02,233587000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleFunction#101][19] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-10 23:47:02,234334000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:20:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744328822214","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"8ed7f105-7254-4084-af1b-17d465ea5fe6"}}
2025-04-10 23:47:02,234480000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-10 23:47:02,234737000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-10 23:47:02,234825000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-10 23:47:02,234894000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-10 23:47:02,234969000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-10 23:47:02,235047000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:21:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-10 23:47:02,235154000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:21:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-10 23:47:02,658893000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:444:425:423] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-10 23:47:02,720947000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:506:487:62] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-10 23:47:02,725603000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:511:492:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-10 23:47:02,725971000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:511:492:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-10 23:47:02,727310000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:513:494:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-10 23:47:02,727413000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:513:494:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-10 23:47:02,727687000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	63	[8ed7f105-7254-4084-af1b-17d465ea5fe6][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:513:494:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-10 23:58:02,796404000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleFunction#101][210] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-10 23:58:02,797240000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:211:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744329482586","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"4b6f03b7-5aa6-4609-b16f-f986a002bd5f"}}
2025-04-10 23:58:02,797400000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:211:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-10 23:58:02,797693000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:211:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-10 23:58:02,797799000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:211:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-10 23:58:02,797881000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:211:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-10 23:58:02,797961000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:211:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-10 23:58:02,798059000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:212:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-10 23:58:02,798182000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:212:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-10 23:58:03,324719000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:738:528:526] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-10 23:58:03,384695000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:798:588:60] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-10 23:58:03,388318000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:802:592:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-10 23:58:03,388665000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:802:592:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-10 23:58:03,389862000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:803:593:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-10 23:58:03,389960000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:803:593:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-10 23:58:03,390235000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	68	[4b6f03b7-5aa6-4609-b16f-f986a002bd5f][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:804:594:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
