2025-04-11 03:01:02,704580000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleFunction#101][27] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 03:01:02,705338000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:28:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744340462677","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"074ab5e8-cf52-487b-bb62-6b3bd8505b33"}}
2025-04-11 03:01:02,705475000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:28:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 03:01:02,705933000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:28:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 03:01:02,706038000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:29:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 03:01:02,706101000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:29:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 03:01:02,706159000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:29:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 03:01:02,706226000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:29:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 03:01:02,706310000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:29:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 03:01:03,524524000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:847:820:818] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 03:01:03,587492000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:910:883:63] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 03:01:03,590863000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:913:886:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 03:01:03,591143000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:914:887:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 03:01:03,593299000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:916:889:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 03:01:03,593383000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:916:889:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 03:01:03,593654000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	123	[074ab5e8-cf52-487b-bb62-6b3bd8505b33][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:916:889:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 03:08:02,562206000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleFunction#101][19] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 03:08:02,562832000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:19:0:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744340882543","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"b4c9b28d-634b-4e75-9b79-103d6b3b1f1e"}}
2025-04-11 03:08:02,562959000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:19:0:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 03:08:02,563249000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:20:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 03:08:02,563327000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 03:08:02,563382000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 03:08:02,563434000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 03:08:02,563496000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 03:08:02,563578000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 03:08:03,027296000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:484:465:464] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 03:08:03,084223000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:541:522:57] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 03:08:03,086984000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:543:524:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 03:08:03,087249000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:544:525:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 03:08:03,088292000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:545:526:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 03:08:03,088368000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:545:526:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 03:08:03,088616000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	128	[b4c9b28d-634b-4e75-9b79-103d6b3b1f1e][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:545:526:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 03:11:02,498286000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleFunction#101][35] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 03:11:02,498954000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:35:0:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744341062463","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"7615da4d-5237-41c1-945c-b3beb090ef0b"}}
2025-04-11 03:11:02,499083000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:36:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 03:11:02,499320000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:36:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 03:11:02,499393000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:36:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 03:11:02,499448000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:36:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 03:11:02,499500000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:36:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 03:11:02,499575000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:36:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 03:11:02,499656000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:36:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 03:11:03,249813000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:786:751:750] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 03:11:03,315862000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:852:817:66] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 03:11:03,318573000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:855:820:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 03:11:03,318894000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:855:820:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 03:11:03,319986000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:856:821:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 03:11:03,320074000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:857:822:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 03:11:03,320306000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	133	[7615da4d-5237-41c1-945c-b3beb090ef0b][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:857:822:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 03:28:02,847539000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleFunction#101][18] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 03:28:02,848203000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:19:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744342082829","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"67626b62-cfdd-485a-8d16-c83a3aec3867"}}
2025-04-11 03:28:02,848325000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 03:28:02,848547000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 03:28:02,848652000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 03:28:02,848714000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 03:28:02,848767000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 03:28:02,848832000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 03:28:02,848912000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 03:28:03,259064000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:429:411:410] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 03:28:03,317687000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:488:470:59] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 03:28:03,321314000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:492:474:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 03:28:03,321597000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:492:474:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 03:28:03,322669000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:493:475:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 03:28:03,322746000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:493:475:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 03:28:03,322952000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	138	[67626b62-cfdd-485a-8d16-c83a3aec3867][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:493:475:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 03:38:01,840234000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleFunction#101][45] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 03:38:01,840867000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:45:0:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744342681795","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4"}}
2025-04-11 03:38:01,841008000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:45:0:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 03:38:01,841239000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:46:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 03:38:01,841311000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:46:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 03:38:01,841366000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:46:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 03:38:01,841416000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:46:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 03:38:01,841478000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:46:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 03:38:01,841557000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:46:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 03:38:02,261978000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:466:421:420] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 03:38:02,320625000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:525:480:59] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 03:38:02,323509000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:528:483:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 03:38:02,323782000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:528:483:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 03:38:02,324888000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:529:484:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 03:38:02,324964000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:529:484:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 03:38:02,325168000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	143	[b1242b5b-4ab7-42c7-a7af-fbf5ba84cba4][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:530:485:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 03:47:01,811061000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleFunction#101][19] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 03:47:01,811731000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:20:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744343221791","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"ff380b11-95d8-4cbe-b19f-88137e3602af"}}
2025-04-11 03:47:01,811854000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 03:47:01,812070000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:21:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 03:47:01,812137000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:21:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 03:47:01,812201000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:21:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 03:47:01,812252000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:21:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 03:47:01,812316000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:21:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 03:47:01,812397000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:21:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 03:47:02,215422000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:424:405:403] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 03:47:02,271881000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:480:461:56] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 03:47:02,274866000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:483:464:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 03:47:02,275177000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:484:465:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 03:47:02,276192000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:485:466:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 03:47:02,276264000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:485:466:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 03:47:02,276470000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	148	[ff380b11-95d8-4cbe-b19f-88137e3602af][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:485:466:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 03:49:01,935467000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleFunction#101][15] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 03:49:01,936144000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:16:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744343341920","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"a1d3b41c-a005-4177-bbb0-5901506f19c9"}}
2025-04-11 03:49:01,936268000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:16:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 03:49:01,936459000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:16:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 03:49:01,936516000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:16:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 03:49:01,936562000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:16:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 03:49:01,936632000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:16:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 03:49:01,936685000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:16:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 03:49:01,936751000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:16:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 03:49:02,560924000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:640:625:624] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 03:49:02,610039000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:689:674:49] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 03:49:02,613250000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:693:678:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 03:49:02,613564000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:693:678:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 03:49:02,614724000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:694:679:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 03:49:02,614796000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:694:679:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 03:49:02,614990000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	153	[a1d3b41c-a005-4177-bbb0-5901506f19c9][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:694:679:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 03:51:01,895710000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleFunction#101][19] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 03:51:01,896298000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:20:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744343461876","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"2e22d8c1-629e-4c93-bd0d-0024fea3ae4d"}}
2025-04-11 03:51:01,896414000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 03:51:01,896683000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 03:51:01,896764000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 03:51:01,896813000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 03:51:01,896858000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 03:51:01,896916000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 03:51:01,896990000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 03:51:02,229805000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:353:334:333] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 03:51:02,286800000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:410:391:57] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 03:51:02,291332000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:415:396:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 03:51:02,291666000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:415:396:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 03:51:02,292637000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:416:397:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 03:51:02,292706000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:416:397:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 03:51:02,292923000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[2e22d8c1-629e-4c93-bd0d-0024fea3ae4d][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:416:397:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 03:52:01,830862000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleFunction#101][19] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 03:52:01,831289000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:20:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744343521811","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"e0c90b7d-3c3f-4e12-8552-cf7875a552f5"}}
2025-04-11 03:52:01,831388000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 03:52:01,831600000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 03:52:01,831653000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 03:52:01,831686000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 03:52:01,831719000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 03:52:01,831762000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 03:52:01,831837000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 03:52:02,239671000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:428:409:408] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 03:52:02,302358000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:491:472:63] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 03:52:02,305132000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:494:475:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 03:52:02,305334000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:494:475:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 03:52:02,306386000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:495:476:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 03:52:02,306459000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:495:476:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 03:52:02,306677000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	158	[e0c90b7d-3c3f-4e12-8552-cf7875a552f5][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:495:476:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
