2025-04-11 00:15:02,638135000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleFunction#101][32] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 00:15:02,638968000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:32:0:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"FSTEST00000000001"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744330502606","x-apiid":"70_2_2:24:*","x-isnotifiable":"1","x-transactionid":"d35288c5-c471-4f2d-94f2-09f25d39c3c4"}}
2025-04-11 00:15:02,639130000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:33:1:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 00:15:02,639419000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 00:15:02,639520000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 00:15:02,639597000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 00:15:02,639672000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 00:15:02,639757000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 00:15:02,639872000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:33:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 00:15:03,055490000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:449:417:416] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 00:15:03,108786000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:502:470:53] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 00:15:03,113633000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:507:475:2] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 00:15:03,113987000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:507:475:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 00:15:03,115301000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:509:477:2] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 00:15:03,115394000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:509:477:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 00:15:03,115649000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	73	[d35288c5-c471-4f2d-94f2-09f25d39c3c4][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:509:477:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 00:17:02,693425000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleFunction#101][29] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 00:17:02,694134000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:30:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744330622664","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"a87d3c35-81a3-4257-b61c-cd9949287081"}}
2025-04-11 00:17:02,694269000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 00:17:02,694523000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 00:17:02,694602000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 00:17:02,694660000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 00:17:02,694716000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 00:17:02,694782000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 00:17:02,694872000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:30:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 00:17:03,476137000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:811:782:781] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 00:17:03,528256000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:864:835:53] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 00:17:03,531817000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:867:838:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 00:17:03,532156000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:868:839:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 00:17:03,533376000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:869:840:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 00:17:03,533473000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:869:840:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 00:17:03,533738000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	78	[a87d3c35-81a3-4257-b61c-cd9949287081][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:869:840:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 00:30:02,835380000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleFunction#101][20] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 00:30:02,836152000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:21:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744331402815","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"0626276c-d215-40ba-99cb-9863a62b9c29"}}
2025-04-11 00:30:02,836323000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 00:30:02,836665000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 00:30:02,836772000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 00:30:02,836846000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 00:30:02,836919000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 00:30:02,837017000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 00:30:02,837139000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:22:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 00:30:03,158282000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:343:323:321] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 00:30:03,218245000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:403:383:60] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 00:30:03,221439000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:406:386:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 00:30:03,221776000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:406:386:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 00:30:03,223140000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:408:388:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 00:30:03,223242000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:408:388:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 00:30:03,223497000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	83	[0626276c-d215-40ba-99cb-9863a62b9c29][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:408:388:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 00:37:01,784979000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleFunction#101][25] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 00:37:01,785792000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:26:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744331821759","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f"}}
2025-04-11 00:37:01,785948000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:26:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 00:37:01,786226000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:27:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 00:37:01,786309000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:27:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 00:37:01,786375000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:27:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 00:37:01,786440000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:27:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 00:37:01,786516000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:27:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 00:37:01,786623000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:27:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 00:37:02,323446000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:564:539:537] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 00:37:02,376272000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:617:592:53] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 00:37:02,379829000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:620:595:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 00:37:02,380160000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:621:596:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 00:37:02,381478000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:622:597:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 00:37:02,381569000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:622:597:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 00:37:02,381807000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	88	[f0b3fca9-81e9-4477-bf95-0ff7b2e02f1f][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:622:597:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 00:46:01,931333000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleFunction#101][28] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 00:46:01,932125000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:29:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744332361903","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"aa5d0b5c-4d3c-4402-a203-6dde06c62b7a"}}
2025-04-11 00:46:01,932285000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:29:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 00:46:01,932561000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:29:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 00:46:01,932683000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:29:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 00:46:01,932736000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:29:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 00:46:01,932786000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:29:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 00:46:01,932846000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:29:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 00:46:01,932931000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:29:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 00:46:02,386981000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:483:455:454] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 00:46:02,434442000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:531:503:48] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 00:46:02,438025000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:534:506:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 00:46:02,438371000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:535:507:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 00:46:02,439475000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:536:508:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 00:46:02,439561000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:536:508:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 00:46:02,439842000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	93	[aa5d0b5c-4d3c-4402-a203-6dde06c62b7a][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:536:508:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 00:48:02,643152000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleFunction#101][21] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 00:48:02,643894000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:21:0:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744332482622","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"315503a7-c757-4407-8ae5-45b8c9559509"}}
2025-04-11 00:48:02,644035000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:22:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 00:48:02,644334000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 00:48:02,644436000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 00:48:02,644501000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 00:48:02,644564000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 00:48:02,644681000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 00:48:02,644790000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 00:48:03,631978000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:1009:988:987] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 00:48:03,691863000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:1069:1048:60] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 00:48:03,695646000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:1073:1052:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 00:48:03,695980000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:1073:1052:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 00:48:03,697166000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:1075:1054:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 00:48:03,697256000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:1075:1054:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 00:48:03,697500000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	98	[315503a7-c757-4407-8ae5-45b8c9559509][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:1075:1054:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 00:50:01,908856000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleFunction#101][20] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 00:50:01,909608000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:21:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744332601888","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"70e35706-f697-4569-974c-df879aa7f19c"}}
2025-04-11 00:50:01,909754000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:21:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 00:50:01,910048000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:22:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 00:50:01,910136000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:22:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 00:50:01,910202000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:22:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 00:50:01,910263000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:22:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 00:50:01,910348000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][ConnectionWithSqlDb#1001][UserListFromVehicleFunction#101:UserListFromVehicleApplication#121:22:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create str
2025-04-11 00:50:01,910466000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][ConnectionWithSqlDb#1003][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1001:22:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create str
2025-04-11 00:50:02,880279000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][ConnectionWithSqlDb#1004][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1003:992:972:970] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db token create fin
2025-04-11 00:50:02,937951000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][ConnectionWithSqlDb#1002][UserListFromVehicleFunction#101:ConnectionWithSqlDb#1004:1049:1029:57] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] db connection create fin
2025-04-11 00:50:02,941527000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:1053:1033:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 00:50:02,941831000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:1053:1033:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 00:50:02,942996000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:1054:1034:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 00:50:02,943094000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:1055:1035:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 00:50:02,943345000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	103	[70e35706-f697-4569-974c-df879aa7f19c][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:1055:1035:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
