2025-04-11 04:02:02,210059000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleFunction#101][18] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 04:02:02,210683000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:19:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744344122191","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"4e6802ba-118d-421d-a776-4a6975bd1b39"}}
2025-04-11 04:02:02,210808000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 04:02:02,210994000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:19:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 04:02:02,211041000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:20:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 04:02:02,211073000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:20:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 04:02:02,211104000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:20:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 04:02:02,215572000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:24:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 04:02:02,215836000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:24:6:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:02:02,217082000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:26:8:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 04:02:02,217184000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:26:8:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 04:02:02,217445000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	166	[4e6802ba-118d-421d-a776-4a6975bd1b39][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:26:8:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 04:14:02,134876000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleFunction#101][21] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 04:14:02,135442000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:22:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744344842113","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"ddb94b21-3871-4da1-995a-f5cf817eabb6"}}
2025-04-11 04:14:02,135548000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 04:14:02,135718000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 04:14:02,135775000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 04:14:02,135815000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 04:14:02,135855000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:22:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 04:14:02,139673000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:26:5:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 04:14:02,139944000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:26:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:14:02,141090000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:28:7:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 04:14:02,141166000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:28:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 04:14:02,141359000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	168	[ddb94b21-3871-4da1-995a-f5cf817eabb6][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:28:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 04:25:02,366021000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleFunction#101][26] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 04:25:02,366675000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:27:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744345502339","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04"}}
2025-04-11 04:25:02,366814000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:27:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 04:25:02,366985000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:27:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 04:25:02,367063000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:28:2:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 04:25:02,367119000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:28:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 04:25:02,367160000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:28:2:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 04:25:02,371255000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:32:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 04:25:02,371548000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:32:6:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:25:02,372666000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:33:7:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 04:25:02,372746000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:33:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 04:25:02,372941000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	170	[57dfd1c9-ff5e-44f7-adc7-60cfc20f9b04][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:33:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 04:33:02,674779000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleFunction#101][25] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 04:33:02,675251000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:26:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744345982649","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"9b92c633-c82e-4f9c-92a0-88178eb4d05e"}}
2025-04-11 04:33:02,675344000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:26:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 04:33:02,675484000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:26:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 04:33:02,675530000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:26:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 04:33:02,675565000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:26:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 04:33:02,675596000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:26:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 04:33:02,679106000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:30:5:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 04:33:02,679329000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:30:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:33:02,680171000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:31:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 04:33:02,680230000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:31:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 04:33:02,680384000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	172	[9b92c633-c82e-4f9c-92a0-88178eb4d05e][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:31:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 04:38:02,635897000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleFunction#101][16] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 04:38:02,636396000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:17:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744346282619","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"f081a02c-2819-4bc4-b9b3-73cc90b6fcc7"}}
2025-04-11 04:38:02,636490000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:17:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 04:38:02,636690000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:17:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 04:38:02,636750000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:17:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 04:38:02,636783000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:17:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 04:38:02,636813000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:17:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 04:38:02,640438000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:21:5:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 04:38:02,640686000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:21:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:38:02,641521000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:22:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 04:38:02,641591000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:22:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 04:38:02,641780000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[f081a02c-2819-4bc4-b9b3-73cc90b6fcc7][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:22:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 04:39:02,285708000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleFunction#101][14] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 04:39:02,286199000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:15:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744346342271","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"4f86011f-93f4-440f-a68e-a2c199fae426"}}
2025-04-11 04:39:02,286310000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:15:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 04:39:02,286495000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:15:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 04:39:02,286553000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:15:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 04:39:02,286594000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:15:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 04:39:02,286632000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:15:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 04:39:02,289940000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:18:4:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 04:39:02,290102000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:19:5:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:39:02,291085000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:20:6:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 04:39:02,291167000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:20:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 04:39:02,291351000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	174	[4f86011f-93f4-440f-a68e-a2c199fae426][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:20:6:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 04:43:02,616864000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleFunction#101][19] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 04:43:02,617510000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:20:1:1] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"PANAEU20000000012"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744346582597","x-apiid":"20_2_1:20:*","x-isnotifiable":"0","x-transactionid":"1e745e91-e1e5-45d5-9249-16ae67ee202f"}}
2025-04-11 04:43:02,617634000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 04:43:02,617800000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 04:43:02,617853000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 04:43:02,617905000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 04:43:02,617943000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:20:1:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 04:43:02,621724000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:24:5:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 04:43:02,622032000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:24:5:0] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:43:02,623034000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:26:7:2] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 04:43:02,623096000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:26:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 04:43:02,623262000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	176	[1e745e91-e1e5-45d5-9249-16ae67ee202f][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:26:7:0] [20_2_1:20:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
2025-04-11 04:59:03,219823000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleFunction#101][25] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run str
2025-04-11 04:59:03,220397000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleFunction#103][UserListFromVehicleFunction#101:UserListFromVehicleFunction#101:26:1:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #2 run request data= {"query":{"vin":"MAZDATR1000000021"},"header":{"host":"cst-01.cv.internal:22080","user-agent":"Java-http-client/11.0.18","content-length":"0","x-acceptdate":"1744347543194","x-apiid":"70_2_2:24:*","x-isnotifiable":"1","x-transactionid":"ff8065a9-efd1-4bf7-892e-3a9fd3b71008"}}
2025-04-11 04:59:03,220514000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleApplication#101][UserListFromVehicleFunction#101:UserListFromVehicleFunction#103:26:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #3 perform str
2025-04-11 04:59:03,220703000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleApplication#111][UserListFromVehicleFunction#101:UserListFromVehicleApplication#101:26:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter str
2025-04-11 04:59:03,220763000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleApplication#113][UserListFromVehicleFunction#101:UserListFromVehicleApplication#111:26:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4-1 checkParameter result= true
2025-04-11 04:59:03,220802000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleApplication#112][UserListFromVehicleFunction#101:UserListFromVehicleApplication#113:26:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #4 checkParameter fin
2025-04-11 04:59:03,220839000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleApplication#121][UserListFromVehicleFunction#101:UserListFromVehicleApplication#112:26:1:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #5 performInner str
2025-04-11 04:59:03,224521000	E	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleApplication#400][UserListFromVehicleFunction#101:CvVehicleManager#104:30:5:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #400 operation error code= 400404 reason= CV Vehicle Not Found
2025-04-11 04:59:03,225268000	F	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleApplication#999][UserListFromVehicleFunction#101:UserListFromVehicleApplication#400:31:6:1] mazda.tk2.customerInfo.server.imp.exception.CustomerInfoOperationErrorException
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.performInner(UserListFromVehicleApplication.java:231)
	mazda.tk2.customerInfo.server.imp.application.UserListFromVehicleApplication.perform(UserListFromVehicleApplication.java:133)
	mazda.tk2.customerInfo.server.imp.functions.UserListFromVehicleFunction.run(UserListFromVehicleFunction.java:108)
	jdk.internal.reflect.GeneratedMethodAccessor2.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:59:03,226411000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleApplication#131][UserListFromVehicleFunction#101:UserListFromVehicleApplication#?:32:7:1] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #6 createResponse
2025-04-11 04:59:03,226491000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleFunction#104][UserListFromVehicleFunction#101:UserListFromVehicleApplication#131:32:7:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #7 run response data= {"status":"OPERATION_ERROR","resultCode":"CST400404","details":[{"detailCode":"400404","reason":"CV Vehicle Not Found"}]}
2025-04-11 04:59:03,226659000	I	bizf-cst-ext-01-67d9b898cb-6f29n	bizf-cst	24	178	[ff8065a9-efd1-4bf7-892e-3a9fd3b71008][UserListFromVehicleFunction#102][UserListFromVehicleFunction#101:UserListFromVehicleFunction#104:32:7:0] [70_2_2:24:* bizf-cst-ext-01-67d9b898cb-6f29n] #1 run fin
