2025-04-11 04:12:14,032875000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationFunction#101][504] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #1 run str
2025-04-11 04:12:14,094786000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationFunction#103][AdfNotificationFunction#101:AdfNotificationFunction#101:572:68:68] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #2 run request data= {"query":{},"header":{"host":"vhc-01.cv.internal:23080","user-agent":"Java-http-client/11.0.18","content-length":"436","x-acceptdate":"1744344733522","x-apiid":"270_2_4:24:*","x-transactionid":"29d2d692-8679-4ec1-b479-a8325a26aabc"},"body":{"Vin":"MAZDATR1000000333","AdfResult":{"DcmModelYear":"24","DcmDestination":"Eu","CountryCode":"FFF","DcmPartNumber":"KLBC67CM0 ","DcmSupplier":"Ha","DcmGrade":"Md4g","Imei":"355666590010517","EUiccId":"89033023821200000000022829873693","BleSerialNumber":"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","CEcuId":"","VehicleUnitTerminalSerialNumber":"KKTHT00000300000","IccId":"89883011439890102577"}}}
2025-04-11 04:12:14,098955000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationApplication#101][AdfNotificationFunction#101:AdfNotificationFunction#103:576:72:4] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #3 perform str
2025-04-11 04:12:14,099322000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationApplication#111][AdfNotificationFunction#101:AdfNotificationApplication#101:577:73:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #4 checkParameter str
2025-04-11 04:12:14,100261000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationApplication#113][AdfNotificationFunction#101:AdfNotificationApplication#111:578:74:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #4-1 checkParameter result= true
2025-04-11 04:12:14,100383000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationApplication#112][AdfNotificationFunction#101:AdfNotificationApplication#113:578:74:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #4 checkParameter fin
2025-04-11 04:12:16,005730000 FATAL [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationApplication#502][AdfNotificationFunction#101:AdfNotificationManagementManager#101:2483:1979:40] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #500 SQLException code= 500C03 reason= null message= com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000333).
2025-04-11 04:12:16,006520000 FATAL [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationApplication#999][AdfNotificationFunction#101:AdfNotificationApplication#502:2484:1980:1] com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000333).
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1676)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:620)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:540)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7627)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3916)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:268)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:242)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeUpdate(SQLServerPreparedStatement.java:486)
	mazda.tk2.adf.server.imp.domain.db.AdfNotificationManagementManager.insert(AdfNotificationManagementManager.java:95)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.lambda$performInner$0(AdfNotificationApplication.java:159)
	mazda.tk2.middleware.db.sqldb.ctl.TransactionExecutorWithSqlDb.execute(TransactionExecutorWithSqlDb.java:92)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.performInner(AdfNotificationApplication.java:161)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.perform(AdfNotificationApplication.java:114)
	mazda.tk2.adf.server.imp.functions.AdfNotificationFunction.run(AdfNotificationFunction.java:109)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:12:16,007448000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationFunction#104][AdfNotificationFunction#101:AdfNotificationApplication#?:2485:1981:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #7 run response data= {"Message":"Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000333).","ResultCode":"500C03"}
2025-04-11 04:12:16,007834000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=22] [29d2d692-8679-4ec1-b479-a8325a26aabc][AdfNotificationFunction#102][AdfNotificationFunction#101:AdfNotificationFunction#104:2485:1981:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #1 run fin
2025-04-11 04:47:50,362499000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationFunction#101][30] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #1 run str
2025-04-11 04:47:50,363776000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationFunction#103][AdfNotificationFunction#101:AdfNotificationFunction#101:31:1:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #2 run request data= {"query":{},"header":{"host":"vhc-01.cv.internal:23080","user-agent":"Java-http-client/11.0.18","content-length":"436","x-acceptdate":"1744346870332","x-apiid":"270_2_4:24:*","x-transactionid":"82c52330-0e61-4301-8fe8-11d07f62f95c"},"body":{"Vin":"MAZDATR1000000021","AdfResult":{"DcmModelYear":"24","DcmDestination":"Eu","CountryCode":"FFF","DcmPartNumber":"KLBC67CM0 ","DcmSupplier":"Ha","DcmGrade":"Md4g","Imei":"355666590010947","EUiccId":"89033023821200000000022829873596","BleSerialNumber":"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","CEcuId":"","VehicleUnitTerminalSerialNumber":"8606830601044131","IccId":"89883011439890102569"}}}
2025-04-11 04:47:50,364004000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationApplication#101][AdfNotificationFunction#101:AdfNotificationFunction#103:31:1:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #3 perform str
2025-04-11 04:47:50,364323000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationApplication#111][AdfNotificationFunction#101:AdfNotificationApplication#101:32:2:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #4 checkParameter str
2025-04-11 04:47:50,364483000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationApplication#113][AdfNotificationFunction#101:AdfNotificationApplication#111:32:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #4-1 checkParameter result= true
2025-04-11 04:47:50,364585000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationApplication#112][AdfNotificationFunction#101:AdfNotificationApplication#113:32:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #4 checkParameter fin
2025-04-11 04:47:51,406637000 FATAL [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationApplication#502][AdfNotificationFunction#101:AdfNotificationManagementManager#101:1074:1044:3] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #500 SQLException code= 500C03 reason= null message= com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).
2025-04-11 04:47:51,407200000 FATAL [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationApplication#999][AdfNotificationFunction#101:AdfNotificationApplication#502:1075:1045:1] com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1676)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:620)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:540)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7627)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3916)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:268)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:242)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeUpdate(SQLServerPreparedStatement.java:486)
	mazda.tk2.adf.server.imp.domain.db.AdfNotificationManagementManager.insert(AdfNotificationManagementManager.java:95)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.lambda$performInner$0(AdfNotificationApplication.java:159)
	mazda.tk2.middleware.db.sqldb.ctl.TransactionExecutorWithSqlDb.execute(TransactionExecutorWithSqlDb.java:92)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.performInner(AdfNotificationApplication.java:161)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.perform(AdfNotificationApplication.java:114)
	mazda.tk2.adf.server.imp.functions.AdfNotificationFunction.run(AdfNotificationFunction.java:109)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:47:51,407720000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationFunction#104][AdfNotificationFunction#101:AdfNotificationApplication#?:1075:1045:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #7 run response data= {"Message":"Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).","ResultCode":"500C03"}
2025-04-11 04:47:51,407992000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=39] [82c52330-0e61-4301-8fe8-11d07f62f95c][AdfNotificationFunction#102][AdfNotificationFunction#101:AdfNotificationFunction#104:1075:1045:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #1 run fin
