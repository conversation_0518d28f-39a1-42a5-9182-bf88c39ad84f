2025-04-11 04:04:59,212908000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationFunction#101][500] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #1 run str
2025-04-11 04:04:59,280281000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationFunction#103][AdfNotificationFunction#101:AdfNotificationFunction#101:574:74:74] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #2 run request data= {"query":{},"header":{"host":"vhc-01.cv.internal:23080","user-agent":"Java-http-client/11.0.18","content-length":"436","x-acceptdate":"1744344298705","x-apiid":"270_2_4:24:*","x-transactionid":"d583f0e0-1dbe-4e7e-886a-89cd1d4dae77"},"body":{"Vin":"MAZDATR1000000333","AdfResult":{"DcmModelYear":"24","DcmDestination":"Eu","CountryCode":"FFF","DcmPartNumber":"KLBC67CM0 ","DcmSupplier":"Ha","DcmGrade":"Md4g","Imei":"355666590010517","EUiccId":"89033023821200000000022829873693","BleSerialNumber":"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","CEcuId":"","VehicleUnitTerminalSerialNumber":"KKTHT00000300000","IccId":"89883011439890102577"}}}
2025-04-11 04:04:59,284811000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationApplication#101][AdfNotificationFunction#101:AdfNotificationFunction#103:579:79:5] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #3 perform str
2025-04-11 04:04:59,285246000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationApplication#111][AdfNotificationFunction#101:AdfNotificationApplication#101:580:80:1] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #4 checkParameter str
2025-04-11 04:04:59,286315000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationApplication#113][AdfNotificationFunction#101:AdfNotificationApplication#111:581:81:1] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #4-1 checkParameter result= true
2025-04-11 04:04:59,286458000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationApplication#112][AdfNotificationFunction#101:AdfNotificationApplication#113:581:81:0] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #4 checkParameter fin
2025-04-11 04:05:00,830741000 FATAL [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationApplication#502][AdfNotificationFunction#101:AdfNotificationManagementManager#101:2125:1625:50] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #500 SQLException code= 500C03 reason= null message= com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000333).
2025-04-11 04:05:00,831704000 FATAL [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationApplication#999][AdfNotificationFunction#101:AdfNotificationApplication#502:2126:1626:1] com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000333).
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1676)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:620)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:540)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7627)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3916)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:268)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:242)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeUpdate(SQLServerPreparedStatement.java:486)
	mazda.tk2.adf.server.imp.domain.db.AdfNotificationManagementManager.insert(AdfNotificationManagementManager.java:95)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.lambda$performInner$0(AdfNotificationApplication.java:159)
	mazda.tk2.middleware.db.sqldb.ctl.TransactionExecutorWithSqlDb.execute(TransactionExecutorWithSqlDb.java:92)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.performInner(AdfNotificationApplication.java:161)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.perform(AdfNotificationApplication.java:114)
	mazda.tk2.adf.server.imp.functions.AdfNotificationFunction.run(AdfNotificationFunction.java:109)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:05:00,832875000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationFunction#104][AdfNotificationFunction#101:AdfNotificationApplication#?:2127:1627:1] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #7 run response data= {"Message":"Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000333).","ResultCode":"500C03"}
2025-04-11 04:05:00,833425000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=22] [d583f0e0-1dbe-4e7e-886a-89cd1d4dae77][AdfNotificationFunction#102][AdfNotificationFunction#101:AdfNotificationFunction#104:2128:1628:1] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #1 run fin
2025-04-11 04:48:59,816993000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationFunction#101][23] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #1 run str
2025-04-11 04:48:59,818342000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationFunction#103][AdfNotificationFunction#101:AdfNotificationFunction#101:25:2:2] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #2 run request data= {"query":{},"header":{"host":"vhc-01.cv.internal:23080","user-agent":"Java-http-client/11.0.18","content-length":"436","x-acceptdate":"1744346939793","x-apiid":"270_2_4:24:*","x-transactionid":"d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15"},"body":{"Vin":"MAZDATR1000000021","AdfResult":{"DcmModelYear":"24","DcmDestination":"Eu","CountryCode":"FFF","DcmPartNumber":"KLBC67CM0 ","DcmSupplier":"Ha","DcmGrade":"Md4g","Imei":"355666590010947","EUiccId":"89033023821200000000022829873596","BleSerialNumber":"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","CEcuId":"","VehicleUnitTerminalSerialNumber":"8606830601044131","IccId":"89883011439890102569"}}}
2025-04-11 04:48:59,818632000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationApplication#101][AdfNotificationFunction#101:AdfNotificationFunction#103:25:2:0] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #3 perform str
2025-04-11 04:48:59,819069000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationApplication#111][AdfNotificationFunction#101:AdfNotificationApplication#101:25:2:0] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #4 checkParameter str
2025-04-11 04:48:59,819235000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationApplication#113][AdfNotificationFunction#101:AdfNotificationApplication#111:26:3:1] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #4-1 checkParameter result= true
2025-04-11 04:48:59,819343000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationApplication#112][AdfNotificationFunction#101:AdfNotificationApplication#113:26:3:0] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #4 checkParameter fin
2025-04-11 04:49:00,344521000 FATAL [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationApplication#502][AdfNotificationFunction#101:AdfNotificationManagementManager#101:551:528:3] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #500 SQLException code= 500C03 reason= null message= com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).
2025-04-11 04:49:00,345168000 FATAL [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationApplication#999][AdfNotificationFunction#101:AdfNotificationApplication#502:551:528:0] com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1676)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:620)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:540)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7627)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3916)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:268)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:242)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeUpdate(SQLServerPreparedStatement.java:486)
	mazda.tk2.adf.server.imp.domain.db.AdfNotificationManagementManager.insert(AdfNotificationManagementManager.java:95)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.lambda$performInner$0(AdfNotificationApplication.java:159)
	mazda.tk2.middleware.db.sqldb.ctl.TransactionExecutorWithSqlDb.execute(TransactionExecutorWithSqlDb.java:92)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.performInner(AdfNotificationApplication.java:161)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.perform(AdfNotificationApplication.java:114)
	mazda.tk2.adf.server.imp.functions.AdfNotificationFunction.run(AdfNotificationFunction.java:109)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:49:00,345701000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationFunction#104][AdfNotificationFunction#101:AdfNotificationApplication#?:552:529:1] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #7 run response data= {"Message":"Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).","ResultCode":"500C03"}
2025-04-11 04:49:00,345995000 INFO [bizf-vhc-ext-02-5ff85c8f47-88jpg, bizf-vhc, pid=24, tid=39] [d1ecdd3a-4d7c-4ea0-bf46-e3bdc5b89f15][AdfNotificationFunction#102][AdfNotificationFunction#101:AdfNotificationFunction#104:552:529:0] [270_2_4:24:* bizf-vhc-ext-02-5ff85c8f47-88jpg] #1 run fin
