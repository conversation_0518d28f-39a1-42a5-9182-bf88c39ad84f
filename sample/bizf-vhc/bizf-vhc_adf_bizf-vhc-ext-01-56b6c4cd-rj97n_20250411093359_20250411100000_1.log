2025-04-11 09:33:59,228586000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationFunction#101][37] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #1 run str
2025-04-11 09:33:59,229626000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationFunction#103][AdfNotificationFunction#101:AdfNotificationFunction#101:38:1:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #2 run request data= {"query":{},"header":{"host":"vhc-01.cv.internal:23080","user-agent":"Java-http-client/11.0.18","content-length":"436","x-acceptdate":"1744364039191","x-apiid":"270_2_4:24:*","x-transactionid":"2128ac68-5ba6-49e9-b61b-2508068ce5ca"},"body":{"Vin":"MAZDATR1000000555","AdfResult":{"DcmModelYear":"24","DcmDestination":"Eu","CountryCode":"FFF","DcmPartNumber":"KLBC67CM0 ","DcmSupplier":"Ha","DcmGrade":"Md4g","Imei":"860683060108257","EUiccId":"89033023821200000000019216637855","BleSerialNumber":"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","CEcuId":"","VehicleUnitTerminalSerialNumber":"KKTHT00000200000","IccId":"89883011369896034039"}}}
2025-04-11 09:33:59,229791000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationApplication#101][AdfNotificationFunction#101:AdfNotificationFunction#103:38:1:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #3 perform str
2025-04-11 09:33:59,230089000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationApplication#111][AdfNotificationFunction#101:AdfNotificationApplication#101:39:2:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #4 checkParameter str
2025-04-11 09:33:59,230219000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationApplication#113][AdfNotificationFunction#101:AdfNotificationApplication#111:39:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #4-1 checkParameter result= true
2025-04-11 09:33:59,230293000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationApplication#112][AdfNotificationFunction#101:AdfNotificationApplication#113:39:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #4 checkParameter fin
2025-04-11 09:33:59,794266000 FATAL [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationApplication#502][AdfNotificationFunction#101:AdfNotificationManagementManager#101:603:566:3] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #500 SQLException code= 500C03 reason= null message= com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000555).
2025-04-11 09:33:59,794724000 FATAL [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationApplication#999][AdfNotificationFunction#101:AdfNotificationApplication#502:603:566:0] com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000555).
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1676)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:620)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:540)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7627)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3916)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:268)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:242)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeUpdate(SQLServerPreparedStatement.java:486)
	mazda.tk2.adf.server.imp.domain.db.AdfNotificationManagementManager.insert(AdfNotificationManagementManager.java:95)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.lambda$performInner$0(AdfNotificationApplication.java:159)
	mazda.tk2.middleware.db.sqldb.ctl.TransactionExecutorWithSqlDb.execute(TransactionExecutorWithSqlDb.java:92)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.performInner(AdfNotificationApplication.java:161)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.perform(AdfNotificationApplication.java:114)
	mazda.tk2.adf.server.imp.functions.AdfNotificationFunction.run(AdfNotificationFunction.java:109)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 09:33:59,795291000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationFunction#104][AdfNotificationFunction#101:AdfNotificationApplication#?:604:567:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #7 run response data= {"Message":"Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000555).","ResultCode":"500C03"}
2025-04-11 09:33:59,795583000 INFO [bizf-vhc-ext-01-56b6c4cd-rj97n, bizf-vhc, pid=24, tid=45] [2128ac68-5ba6-49e9-b61b-2508068ce5ca][AdfNotificationFunction#102][AdfNotificationFunction#101:AdfNotificationFunction#104:604:567:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-rj97n] #1 run fin
