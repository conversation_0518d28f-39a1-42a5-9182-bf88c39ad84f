2025-04-11 04:06:08,181340000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationFunction#101][458] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #1 run str
2025-04-11 04:06:08,242945000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationFunction#103][AdfNotificationFunction#101:AdfNotificationFunction#101:526:68:68] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #2 run request data= {"query":{},"header":{"host":"vhc-01.cv.internal:23080","user-agent":"Java-http-client/11.0.18","content-length":"436","x-acceptdate":"1744344367716","x-apiid":"270_2_4:24:*","x-transactionid":"fa378819-95aa-40ca-9265-d688f12c740a"},"body":{"Vin":"MAZDATR1000000333","AdfResult":{"DcmModelYear":"24","DcmDestination":"Eu","CountryCode":"FFF","DcmPartNumber":"KLBC67CM0 ","DcmSupplier":"Ha","DcmGrade":"Md4g","Imei":"355666590010517","EUiccId":"89033023821200000000022829873693","BleSerialNumber":"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","CEcuId":"","VehicleUnitTerminalSerialNumber":"KKTHT00000300000","IccId":"89883011439890102577"}}}
2025-04-11 04:06:08,247161000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationApplication#101][AdfNotificationFunction#101:AdfNotificationFunction#103:531:73:5] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #3 perform str
2025-04-11 04:06:08,247536000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationApplication#111][AdfNotificationFunction#101:AdfNotificationApplication#101:531:73:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #4 checkParameter str
2025-04-11 04:06:08,248433000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationApplication#113][AdfNotificationFunction#101:AdfNotificationApplication#111:532:74:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #4-1 checkParameter result= true
2025-04-11 04:06:08,248563000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationApplication#112][AdfNotificationFunction#101:AdfNotificationApplication#113:532:74:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #4 checkParameter fin
2025-04-11 04:06:10,184357000 FATAL [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationApplication#502][AdfNotificationFunction#101:AdfNotificationManagementManager#101:2468:2010:40] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #500 SQLException code= 500C03 reason= null message= com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000333).
2025-04-11 04:06:10,185108000 FATAL [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationApplication#999][AdfNotificationFunction#101:AdfNotificationApplication#502:2468:2010:0] com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000333).
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1676)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:620)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:540)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7627)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3916)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:268)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:242)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeUpdate(SQLServerPreparedStatement.java:486)
	mazda.tk2.adf.server.imp.domain.db.AdfNotificationManagementManager.insert(AdfNotificationManagementManager.java:95)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.lambda$performInner$0(AdfNotificationApplication.java:159)
	mazda.tk2.middleware.db.sqldb.ctl.TransactionExecutorWithSqlDb.execute(TransactionExecutorWithSqlDb.java:92)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.performInner(AdfNotificationApplication.java:161)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.perform(AdfNotificationApplication.java:114)
	mazda.tk2.adf.server.imp.functions.AdfNotificationFunction.run(AdfNotificationFunction.java:109)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:06:10,186071000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationFunction#104][AdfNotificationFunction#101:AdfNotificationApplication#?:2469:2011:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #7 run response data= {"Message":"Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000333).","ResultCode":"500C03"}
2025-04-11 04:06:10,186464000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=22] [fa378819-95aa-40ca-9265-d688f12c740a][AdfNotificationFunction#102][AdfNotificationFunction#101:AdfNotificationFunction#104:2470:2012:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #1 run fin
2025-04-11 04:50:56,930905000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationFunction#101][61] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #1 run str
2025-04-11 04:50:56,932268000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationFunction#103][AdfNotificationFunction#101:AdfNotificationFunction#101:63:2:2] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #2 run request data= {"query":{},"header":{"host":"vhc-01.cv.internal:23080","user-agent":"Java-http-client/11.0.18","content-length":"436","x-acceptdate":"1744347056869","x-apiid":"270_2_4:24:*","x-transactionid":"374247b2-cbca-438a-a25a-1482fb98dfff"},"body":{"Vin":"MAZDATR1000000021","AdfResult":{"DcmModelYear":"24","DcmDestination":"Eu","CountryCode":"FFF","DcmPartNumber":"KLBC67CM0 ","DcmSupplier":"Ha","DcmGrade":"Md4g","Imei":"355666590010947","EUiccId":"89033023821200000000022829873596","BleSerialNumber":"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","CEcuId":"","VehicleUnitTerminalSerialNumber":"8606830601044131","IccId":"89883011439890102569"}}}
2025-04-11 04:50:56,932485000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationApplication#101][AdfNotificationFunction#101:AdfNotificationFunction#103:63:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #3 perform str
2025-04-11 04:50:56,932822000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationApplication#111][AdfNotificationFunction#101:AdfNotificationApplication#101:63:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #4 checkParameter str
2025-04-11 04:50:56,932986000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationApplication#113][AdfNotificationFunction#101:AdfNotificationApplication#111:63:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #4-1 checkParameter result= true
2025-04-11 04:50:56,933092000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationApplication#112][AdfNotificationFunction#101:AdfNotificationApplication#113:64:3:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #4 checkParameter fin
2025-04-11 04:50:57,429837000 FATAL [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationApplication#502][AdfNotificationFunction#101:AdfNotificationManagementManager#101:560:499:3] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #500 SQLException code= 500C03 reason= null message= com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).
2025-04-11 04:50:57,430421000 FATAL [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationApplication#999][AdfNotificationFunction#101:AdfNotificationApplication#502:561:500:1] com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1676)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:620)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:540)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7627)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3916)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:268)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:242)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeUpdate(SQLServerPreparedStatement.java:486)
	mazda.tk2.adf.server.imp.domain.db.AdfNotificationManagementManager.insert(AdfNotificationManagementManager.java:95)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.lambda$performInner$0(AdfNotificationApplication.java:159)
	mazda.tk2.middleware.db.sqldb.ctl.TransactionExecutorWithSqlDb.execute(TransactionExecutorWithSqlDb.java:92)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.performInner(AdfNotificationApplication.java:161)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.perform(AdfNotificationApplication.java:114)
	mazda.tk2.adf.server.imp.functions.AdfNotificationFunction.run(AdfNotificationFunction.java:109)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:50:57,431085000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationFunction#104][AdfNotificationFunction#101:AdfNotificationApplication#?:562:501:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #7 run response data= {"Message":"Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).","ResultCode":"500C03"}
2025-04-11 04:50:57,431395000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=39] [374247b2-cbca-438a-a25a-1482fb98dfff][AdfNotificationFunction#102][AdfNotificationFunction#101:AdfNotificationFunction#104:562:501:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #1 run fin
2025-04-11 04:54:59,854905000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationFunction#101][27] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #1 run str
2025-04-11 04:54:59,856078000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationFunction#103][AdfNotificationFunction#101:AdfNotificationFunction#101:28:1:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #2 run request data= {"query":{},"header":{"host":"vhc-01.cv.internal:23080","user-agent":"Java-http-client/11.0.18","content-length":"436","x-acceptdate":"1744347299827","x-apiid":"270_2_4:24:*","x-transactionid":"bb37ada1-c59e-4815-b8a3-d5ad26760ad7"},"body":{"Vin":"MAZDATR1000000021","AdfResult":{"DcmModelYear":"24","DcmDestination":"Eu","CountryCode":"FFF","DcmPartNumber":"KLBC67CM0 ","DcmSupplier":"Ha","DcmGrade":"Md4g","Imei":"355666590010947","EUiccId":"89033023821200000000022829873596","BleSerialNumber":"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","CEcuId":"","VehicleUnitTerminalSerialNumber":"8606830601044131","IccId":"89883011439890102569"}}}
2025-04-11 04:54:59,856287000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationApplication#101][AdfNotificationFunction#101:AdfNotificationFunction#103:29:2:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #3 perform str
2025-04-11 04:54:59,856616000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationApplication#111][AdfNotificationFunction#101:AdfNotificationApplication#101:29:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #4 checkParameter str
2025-04-11 04:54:59,856755000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationApplication#113][AdfNotificationFunction#101:AdfNotificationApplication#111:29:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #4-1 checkParameter result= true
2025-04-11 04:54:59,856848000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationApplication#112][AdfNotificationFunction#101:AdfNotificationApplication#113:29:2:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #4 checkParameter fin
2025-04-11 04:55:00,456933000 FATAL [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationApplication#502][AdfNotificationFunction#101:AdfNotificationManagementManager#101:629:602:2] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #500 SQLException code= 500C03 reason= null message= com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).
2025-04-11 04:55:00,457446000 FATAL [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationApplication#999][AdfNotificationFunction#101:AdfNotificationApplication#502:630:603:1] com.microsoft.sqlserver.jdbc.SQLServerException: Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1676)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:620)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:540)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7627)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3916)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:268)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:242)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeUpdate(SQLServerPreparedStatement.java:486)
	mazda.tk2.adf.server.imp.domain.db.AdfNotificationManagementManager.insert(AdfNotificationManagementManager.java:95)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.lambda$performInner$0(AdfNotificationApplication.java:159)
	mazda.tk2.middleware.db.sqldb.ctl.TransactionExecutorWithSqlDb.execute(TransactionExecutorWithSqlDb.java:92)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.performInner(AdfNotificationApplication.java:161)
	mazda.tk2.adf.server.imp.application.AdfNotificationApplication.perform(AdfNotificationApplication.java:114)
	mazda.tk2.adf.server.imp.functions.AdfNotificationFunction.run(AdfNotificationFunction.java:109)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:566)
	com.microsoft.azure.functions.worker.broker.JavaMethodInvokeInfo.invoke(JavaMethodInvokeInfo.java:22)
	com.microsoft.azure.functions.worker.broker.EnhancedJavaMethodExecutorImpl.execute(EnhancedJavaMethodExecutorImpl.java:22)
	com.microsoft.azure.functions.worker.chain.FunctionExecutionMiddleware.invoke(FunctionExecutionMiddleware.java:19)
	com.microsoft.azure.functions.worker.chain.InvocationChain.doNext(InvocationChain.java:21)
	com.microsoft.azure.functions.worker.broker.JavaFunctionBroker.invokeMethod(JavaFunctionBroker.java:125)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:34)
	com.microsoft.azure.functions.worker.handler.InvocationRequestHandler.execute(InvocationRequestHandler.java:10)
	com.microsoft.azure.functions.worker.handler.MessageHandler.handle(MessageHandler.java:44)
	com.microsoft.azure.functions.worker.JavaWorkerClient$StreamingMessagePeer.lambda$onNext$0(JavaWorkerClient.java:94)
	java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	java.base/java.lang.Thread.run(Thread.java:829)
2025-04-11 04:55:00,458086000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationFunction#104][AdfNotificationFunction#101:AdfNotificationApplication#?:631:604:1] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #7 run response data= {"Message":"Violation of PRIMARY KEY constraint 'PK_AdfNotificationManagement'. Cannot insert duplicate key in object 'model24.AdfNotificationManagement'. The duplicate key value is (MAZDATR1000000021).","ResultCode":"500C03"}
2025-04-11 04:55:00,458371000 INFO [bizf-vhc-ext-01-56b6c4cd-9hmww, bizf-vhc, pid=24, tid=45] [bb37ada1-c59e-4815-b8a3-d5ad26760ad7][AdfNotificationFunction#102][AdfNotificationFunction#101:AdfNotificationFunction#104:631:604:0] [270_2_4:24:* bizf-vhc-ext-01-56b6c4cd-9hmww] #1 run fin
