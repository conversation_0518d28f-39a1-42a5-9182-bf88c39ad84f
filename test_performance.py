#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để test hiệu suất của log generator với các cải tiến mới.
"""

import os
import sys
import time
import tempfile
import shutil
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config_loader import ConfigLoader
from src.log_generator import LogGenerator
from src.log_writer import LogWriter

def test_performance():
    """Test hiệu suất với các cấu hình kh<PERSON>c nhau."""
    
    # Tạo thư mục tạm
    temp_dir = tempfile.mkdtemp(prefix="log_perf_test_")
    print(f"📁 Test directory: {temp_dir}")
    
    try:
        # Load config
        config = ConfigLoader("config.yaml")
        
        # Test parameters
        test_cases = [
            {"lines": 50000, "threads": 1, "compress": False, "name": "50K lines, 1 thread, no compression"},
            {"lines": 50000, "threads": 4, "compress": False, "name": "50K lines, 4 threads, no compression"},
            {"lines": 50000, "threads": 8, "compress": False, "name": "50K lines, 8 threads, no compression"},
            {"lines": 50000, "threads": 4, "compress": True, "name": "50K lines, 4 threads, with compression"},
            {"lines": 100000, "threads": 8, "compress": True, "name": "100K lines, 8 threads, with compression"},
        ]
        
        app_name = "apf-dsn-flow"
        
        print("\n🚀 Starting performance tests...")
        print("=" * 80)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📊 Test {i}/{len(test_cases)}: {test_case['name']}")
            print("-" * 60)
            
            # Setup
            generator = LogGenerator(config)
            writer = LogWriter(config)
            writer.set_base_dir(temp_dir)
            writer.set_compress(test_case['compress'])
            
            # Generate logs
            start_time = time.time()
            
            if test_case['threads'] == 1:
                # Single thread
                log_generator = generator._generate_log_lines(app_name, test_case['lines'])
            else:
                # Multi thread
                log_generator = generator._generate_log_lines_threaded(
                    app_name, test_case['lines'], max_workers=test_case['threads']
                )
            
            # Write to file
            file_path = writer.write_logs_in_chunks(
                app_name,
                log_generator,
                total_lines=test_case['lines'],
                chunk_size=10000
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Get file info
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            
            # Calculate metrics
            lines_per_sec = test_case['lines'] / total_time
            mb_per_sec = file_size_mb / total_time
            
            print(f"✓ Time: {total_time:.2f} seconds")
            print(f"✓ File size: {file_size_mb:.2f} MB")
            print(f"✓ Speed: {lines_per_sec:,.0f} lines/sec")
            print(f"✓ Throughput: {mb_per_sec:.2f} MB/sec")
            print(f"✓ File: {os.path.basename(file_path)}")
            
            # Clean up this test file
            if os.path.exists(file_path):
                os.remove(file_path)
        
        print("\n" + "=" * 80)
        print("🎉 Performance tests completed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up temp directory
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 Cleaned up test directory: {temp_dir}")
        except Exception as e:
            print(f"⚠️  Could not clean up {temp_dir}: {e}")

def test_compression_comparison():
    """So sánh hiệu suất compression cũ vs mới."""
    
    temp_dir = tempfile.mkdtemp(prefix="log_compress_test_")
    print(f"\n📁 Compression test directory: {temp_dir}")
    
    try:
        config = ConfigLoader("config.yaml")
        app_name = "apf-dsn-flow"
        lines = 30000
        
        print("\n🔄 Testing compression methods...")
        print("=" * 60)
        
        # Test 1: New method (write then compress)
        print("\n📝 Test 1: Write uncompressed then compress")
        generator = LogGenerator(config)
        writer = LogWriter(config)
        writer.set_base_dir(temp_dir)
        writer.set_compress(True)
        
        start_time = time.time()
        log_generator = generator._generate_log_lines_threaded(app_name, lines, max_workers=4)
        file_path = writer.write_logs_in_chunks(app_name, log_generator, total_lines=lines)
        new_method_time = time.time() - start_time
        
        new_file_size = os.path.getsize(file_path)
        print(f"✓ Time: {new_method_time:.2f} seconds")
        print(f"✓ File size: {new_file_size / (1024*1024):.2f} MB")
        print(f"✓ Speed: {lines/new_method_time:,.0f} lines/sec")
        
        # Clean up
        if os.path.exists(file_path):
            os.remove(file_path)
        
        print("\n" + "=" * 60)
        print("📊 Compression test completed!")
        
    except Exception as e:
        print(f"❌ Error during compression testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 Cleaned up compression test directory")
        except Exception as e:
            print(f"⚠️  Could not clean up {temp_dir}: {e}")

def main():
    """Main function."""
    print("🧪 LOG GENERATOR PERFORMANCE TEST")
    print("=" * 50)
    
    # Check if config exists
    if not os.path.exists("config.yaml"):
        print("❌ config.yaml not found!")
        print("Please run this script from the project root directory.")
        return
    
    # Run tests
    test_performance()
    test_compression_comparison()
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    main()
