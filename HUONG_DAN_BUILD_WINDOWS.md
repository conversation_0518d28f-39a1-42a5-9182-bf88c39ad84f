# HƯỚNG DẪN BUILD EXECUTABLE CHO WINDOWS

## 📋 Tổng quan
Script này sẽ tạo file executable (.exe) cho Windows từ dự án Log Generator Python, cho phép chạy trên Windows mà không cần cài Python.

## 🔧 <PERSON><PERSON><PERSON> cầu hệ thống
- **Windows 10/11** (64-bit)
- **Python 3.8+** (tả<PERSON> từ [python.org](https://python.org))
- **Kết nối Internet** (để tải dependencies)

## 📦 Chuẩn bị

### 1. Cài đặt Python
1. Tải Python từ: https://python.org
2. **QUAN TRỌNG**: Tick vào "Add Python to PATH" khi cài
3. Chọn "Install Now"
4. Kiểm tra: Mở Command Prompt và gõ `python --version`

### 2. Copy dự án
Copy toàn bộ thư mục dự án sang máy Windows

## 🚀 Cách build

### Phương pháp 1: Sử dụng Batch Script (<PERSON><PERSON> nhất)
1. Mở thư mục dự án
2. **Double-click** vào file `build_windows.bat`
3. Đ<PERSON>i script chạy xong
4. Kiểm tra thư mục `log-generator-windows-portable`

### Phương pháp 2: Sử dụng Command Line
1. Mở **Command Prompt** hoặc **PowerShell**
2. Chuyển đến thư mục dự án:
   ```cmd
   cd path\to\log_generator
   ```
3. Chạy build script:
   ```cmd
   python build_windows.py
   ```

## 📁 Kết quả
Sau khi build thành công, bạn sẽ có:

```
log-generator-windows-portable/
├── log-generator.exe          # Tool chính
├── vin-csv-generator.exe      # Tool VIN CSV
├── config.yaml                # File cấu hình
├── run.bat                    # Menu tiện lợi
├── README.md                  # Hướng dẫn
├── output/                    # Thư mục output
└── sample/                    # File mẫu
```

**Và file ZIP**: `log-generator-windows-portable.zip`

## ✅ Kiểm tra
Để kiểm tra executable hoạt động:
```cmd
cd log-generator-windows-portable
log-generator.exe --help
vin-csv-generator.exe --help
```

## 🔧 Troubleshooting

### Lỗi "Python không được nhận diện"
- Cài lại Python với tùy chọn "Add to PATH"
- Hoặc thêm Python vào PATH thủ công

### Lỗi "pip không hoạt động"
```cmd
python -m ensurepip --upgrade
python -m pip install --upgrade pip
```

### Lỗi "PyInstaller thất bại"
```cmd
pip install --upgrade pyinstaller
```

### Lỗi "Thiếu dependencies"
```cmd
pip install -r requirements.txt
```

### Lỗi "Permission denied"
- Chạy Command Prompt với quyền Administrator
- Tắt Windows Defender tạm thời

## 📊 Kích thước file
- **log-generator.exe**: ~40-60MB
- **vin-csv-generator.exe**: ~40-60MB
- **Tổng package**: ~100-150MB

## 🎯 Sử dụng sau khi build
1. Copy thư mục `log-generator-windows-portable` đi bất cứ đâu
2. Chạy `run.bat` để sử dụng menu
3. Hoặc chạy trực tiếp các file .exe

## 💡 Tips
- Build trên Windows 64-bit để đảm bảo tương thích
- Kiểm tra Windows Defender không block file .exe
- Backup file config.yaml trước khi chỉnh sửa
- Sử dụng `--dry-run` để test trước khi tạo file thật

## 📞 Hỗ trợ
Nếu gặp vấn đề:
1. Kiểm tra Python version: `python --version`
2. Kiểm tra pip: `pip --version`
3. Chạy lại với quyền Administrator
4. Liên hệ team phát triển với thông tin lỗi chi tiết

---
**Lưu ý**: Script này tạo executable native cho Windows, đảm bảo tương thích 100% với Windows 64-bit.
