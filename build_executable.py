#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để build executable từ log generator project sử dụng PyInstaller.
Tạo file .exe có thể chạy trên Windows mà không cần cài Python.
"""

import os
import sys
import subprocess
import shutil
import zipfile
from pathlib import Path

def install_pyinstaller():
    """Cài đặt PyInstaller nếu chưa có."""
    try:
        import PyInstaller
        print("✓ PyInstaller đã được cài đặt")
        return True
    except ImportError:
        print("📦 Đang cài đặt PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller đã được cài đặt thành công")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi khi cài PyInstaller: {e}")
            return False

def create_spec_file():
    """Tạo file .spec cho PyInstaller với cấu hình tối ưu."""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/cli.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.yaml', '.'),
        ('src/*.py', 'src'),
        ('sample', 'sample'),
    ],
    hiddenimports=[
        'src.config_loader',
        'src.log_generator', 
        'src.log_writer',
        'src.vin_csv_generator',
        'yaml',
        'tqdm',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='log-generator',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('log-generator.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ Đã tạo file log-generator.spec")

def create_vin_csv_spec_file():
    """Tạo file .spec riêng cho VIN CSV generator."""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/vin_csv_generator.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.yaml', '.'),
        ('src/*.py', 'src'),
    ],
    hiddenimports=[
        'src.config_loader',
        'yaml',
        'tqdm',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='vin-csv-generator',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('vin-csv-generator.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ Đã tạo file vin-csv-generator.spec")

def build_executable():
    """Build executable sử dụng PyInstaller."""
    print("🔨 Đang build executable...")
    
    # Build log generator
    try:
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "log-generator.spec"]
        subprocess.check_call(cmd)
        print("✓ Build log-generator.exe thành công")
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi khi build log-generator: {e}")
        return False
    
    # Build VIN CSV generator
    try:
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "vin-csv-generator.spec"]
        subprocess.check_call(cmd)
        print("✓ Build vin-csv-generator.exe thành công")
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi khi build vin-csv-generator: {e}")
        return False
    
    return True

def create_portable_package():
    """Tạo package portable với tất cả file cần thiết."""
    package_dir = "log-generator-portable"

    # Xóa thư mục cũ nếu có
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)

    # Tạo thư mục package
    os.makedirs(package_dir, exist_ok=True)

    # Copy executable files (trên macOS sẽ không có .exe extension)
    log_gen_exe = "dist/log-generator.exe" if os.path.exists("dist/log-generator.exe") else "dist/log-generator"
    vin_gen_exe = "dist/vin-csv-generator.exe" if os.path.exists("dist/vin-csv-generator.exe") else "dist/vin-csv-generator"

    if os.path.exists(log_gen_exe):
        # Copy và đổi tên thành .exe cho Windows
        target_name = "log-generator.exe"
        shutil.copy2(log_gen_exe, os.path.join(package_dir, target_name))
        print(f"✓ Đã copy {target_name}")
    else:
        print(f"❌ Không tìm thấy log-generator executable")

    if os.path.exists(vin_gen_exe):
        # Copy và đổi tên thành .exe cho Windows
        target_name = "vin-csv-generator.exe"
        shutil.copy2(vin_gen_exe, os.path.join(package_dir, target_name))
        print(f"✓ Đã copy {target_name}")
    else:
        print(f"❌ Không tìm thấy vin-csv-generator executable")
    
    # Copy config file
    if os.path.exists("config.yaml"):
        shutil.copy2("config.yaml", package_dir)
        print("✓ Đã copy config.yaml")
    
    # Copy sample folder
    if os.path.exists("sample"):
        shutil.copytree("sample", os.path.join(package_dir, "sample"))
        print("✓ Đã copy thư mục sample")
    
    # Tạo thư mục output
    os.makedirs(os.path.join(package_dir, "output"), exist_ok=True)
    print("✓ Đã tạo thư mục output")
    
    # Tạo file README cho package
    readme_content = """# Log Generator - Portable Version

## Mô tả
Tool tự động tạo file log dựa vào cấu hình YAML với hỗ trợ VIN data generation.
Phiên bản portable này có thể chạy trên Windows mà không cần cài Python.

## Cách sử dụng

### 1. Log Generator
```cmd
log-generator.exe --help
log-generator.exe --config config.yaml --output-dir output
log-generator.exe --config config.yaml --app-name apf-dsn-flow --file-count 5
```

### 2. VIN CSV Generator  
```cmd
vin-csv-generator.exe --help
vin-csv-generator.exe --config config.yaml --output vin_data.csv --count 100000
```

## Các tham số chính

### Log Generator:
- `--config`: Đường dẫn file cấu hình (mặc định: config.yaml)
- `--output-dir`: Thư mục output (mặc định: output)
- `--app-name`: Tên ứng dụng cụ thể (mặc định: tất cả)
- `--file-count`: Số file tạo ra
- `--size-mb`: Kích thước file (MB)
- `--compress`: Nén file output
- `--threads`: Số thread sử dụng

### VIN CSV Generator:
- `--config`: Đường dẫn file cấu hình
- `--output`: File CSV output
- `--count`: Số bản ghi tạo ra (mặc định: 100000)

## File cấu hình
Chỉnh sửa file `config.yaml` để thay đổi cấu hình log generation.

## Thư mục
- `output/`: Chứa file log được tạo ra
- `sample/`: Chứa file log mẫu
- `config.yaml`: File cấu hình chính

## Hỗ trợ
Liên hệ team phát triển nếu có vấn đề.
"""
    
    with open(os.path.join(package_dir, "README.md"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✓ Đã tạo README.md")
    
    # Tạo batch file để chạy nhanh
    batch_content = """@echo off
echo ========================================
echo    Log Generator - Portable Version
echo ========================================
echo.
echo 1. Chay log generator (tat ca app)
echo 2. Chay log generator (chi dinh app)  
echo 3. Chay VIN CSV generator
echo 4. Xem help
echo.
set /p choice="Chon tuy chon (1-4): "

if "%choice%"=="1" (
    echo Dang chay log generator cho tat ca app...
    log-generator.exe --config config.yaml --output-dir output
) else if "%choice%"=="2" (
    set /p appname="Nhap ten app (vd: apf-dsn-flow): "
    echo Dang chay log generator cho app: %appname%
    log-generator.exe --config config.yaml --app-name %appname% --output-dir output
) else if "%choice%"=="3" (
    set /p count="Nhap so ban ghi VIN (mac dinh 100000): "
    if "%count%"=="" set count=100000
    echo Dang tao %count% ban ghi VIN...
    vin-csv-generator.exe --config config.yaml --output vin_data.csv --count %count%
) else if "%choice%"=="4" (
    echo === Log Generator Help ===
    log-generator.exe --help
    echo.
    echo === VIN CSV Generator Help ===
    vin-csv-generator.exe --help
) else (
    echo Lua chon khong hop le!
)

echo.
pause
"""
    
    with open(os.path.join(package_dir, "run.bat"), 'w', encoding='utf-8') as f:
        f.write(batch_content)
    print("✓ Đã tạo run.bat")
    
    return package_dir

def create_zip_package(package_dir):
    """Tạo file ZIP từ package directory."""
    zip_filename = f"{package_dir}.zip"
    
    if os.path.exists(zip_filename):
        os.remove(zip_filename)
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir)
                zipf.write(file_path, arc_name)
    
    print(f"✓ Đã tạo file ZIP: {zip_filename}")
    return zip_filename

def cleanup():
    """Dọn dẹp file tạm."""
    files_to_remove = [
        "log-generator.spec",
        "vin-csv-generator.spec"
    ]

    dirs_to_remove = [
        "build",
        "__pycache__"
    ]

    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"✓ Đã xóa {file}")

    for dir in dirs_to_remove:
        if os.path.exists(dir):
            shutil.rmtree(dir)
            print(f"✓ Đã xóa thư mục {dir}")

    # Giữ lại thư mục dist để debug nếu cần
    print("📁 Thư mục dist được giữ lại để kiểm tra")

def main():
    """Hàm chính."""
    print("🚀 Bắt đầu build executable cho Log Generator")
    print("=" * 50)
    
    # Kiểm tra và cài PyInstaller
    if not install_pyinstaller():
        return
    
    # Tạo spec files
    create_spec_file()
    create_vin_csv_spec_file()
    
    # Build executable
    if not build_executable():
        return
    
    # Tạo package portable
    package_dir = create_portable_package()
    
    # Tạo file ZIP
    zip_file = create_zip_package(package_dir)
    
    # Dọn dẹp
    cleanup()
    
    print("\n" + "=" * 50)
    print("🎉 BUILD THÀNH CÔNG!")
    print(f"📁 Package folder: {package_dir}")
    print(f"📦 ZIP file: {zip_file}")
    print("\nBạn có thể:")
    print(f"1. Copy thư mục '{package_dir}' sang máy Windows khác")
    print(f"2. Hoặc copy file '{zip_file}' và giải nén trên máy Windows")
    print("3. Chạy 'run.bat' để sử dụng tool")
    print("4. Hoặc chạy trực tiếp các file .exe từ command line")

if __name__ == "__main__":
    main()
