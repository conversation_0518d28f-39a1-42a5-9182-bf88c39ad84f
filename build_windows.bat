@echo off
chcp 65001 >nul
echo ========================================
echo   LOG GENERATOR - WINDOWS BUILD SCRIPT
echo ========================================
echo.
echo Script này sẽ tạo file executable cho Windows
echo.

REM Kiểm tra Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python chưa được cài đặt!
    echo 📝 Hướng dẫn:
    echo 1. Tải Python từ: https://python.org
    echo 2. Cài Python với tùy chọn "Add to PATH"
    echo 3. Chạy lại script này
    pause
    exit /b 1
)

echo ✓ Python đã được cài đặt
python --version

echo.
echo 🔨 Bắt đầu build process...
echo.

REM Chạy Python build script
python build_windows.py

if errorlevel 1 (
    echo.
    echo ❌ Build thất bại!
    echo 📝 Kiểm tra lỗi ở trên và thử lại
    pause
    exit /b 1
)

echo.
echo 🎉 Build hoàn thành!
echo 📁 Kiểm tra thư mục log-generator-windows-portable
echo 📦 Hoặc file ZIP log-generator-windows-portable.zip
echo.
pause
