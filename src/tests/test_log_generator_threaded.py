#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests for multi-threaded log generation functionality.
"""

import unittest
import os
import tempfile
import time
from pathlib import Path
from typing import List

from config_loader import ConfigLoader
from log_generator import LogGenerator
from log_writer import LogWriter


class TestLogGeneratorThreaded(unittest.TestCase):
    """Test cases for multi-threaded log generation."""

    def setUp(self):
        """Set up test fixtures."""
        # Use a sample config file from the test directory
        test_dir = os.path.dirname(os.path.abspath(__file__))
        self.config_path = os.path.join(test_dir, '..', '..', 'config.yaml')
        self.config = ConfigLoader(self.config_path)
        
        # Create a temporary directory for output
        self.temp_dir = tempfile.mkdtemp(prefix='log_gen_test_')
        
        # Initialize generator and writer
        self.generator = LogGenerator(self.config)
        self.writer = LogWriter(self.config)
        self.writer.set_base_dir(self.temp_dir)
    
    def tearDown(self):
        """Clean up after tests."""
        # Clean up temporary directory
        for file_path in Path(self.temp_dir).glob('*'):
            try:
                file_path.unlink()
            except Exception as e:
                print(f"Warning: Could not delete {file_path}: {e}")
        try:
            os.rmdir(self.temp_dir)
        except Exception as e:
            print(f"Warning: Could not remove temp directory {self.temp_dir}: {e}")
    
    def test_threaded_generation_basic(self):
        """Test basic functionality of threaded log generation."""
        app_name = 'apf-dsn-flow'
        total_lines = 1000
        
        # Generate logs with 2 threads
        lines = self.generator.generate_multiple_lines_threaded(
            app_name, total_lines, workers=2
        )
        
        self.assertEqual(len(lines), total_lines)
        self.assertTrue(all(isinstance(line, str) for line in lines))
    
    def test_threaded_generation_vin_uniqueness(self):
        """Test that VINs are unique across threads."""
        app_name = 'apf-dsn-flow'
        total_lines = 1000
        
        # Generate logs with 4 threads
        lines = self.generator.generate_multiple_lines_threaded(
            app_name, total_lines, workers=4
        )
        
        # Extract all VINs from the log lines
        vins = set()
        for line in lines:
            if '__VIN__' in line:  # If the log format contains VINs
                # Extract VIN from the log line (this is a simplified example)
                # Adjust the extraction logic based on your actual log format
                vin = line.split('VIN:')[-1].split()[0]
                vins.add(vin)
        
        # If VINs are in the log format, verify they're all unique
        if vins:
            self.assertEqual(len(vins), total_lines, "All VINs should be unique")
    
    def test_threaded_generation_performance(self):
        """Test that multi-threaded generation is faster than single-threaded."""
        app_name = 'apf-dsn-flow'
        total_lines = 5000
        
        # Time single-threaded generation
        start = time.time()
        single_threaded = self.generator.generate_multiple_lines(app_name, total_lines)
        single_threaded_time = time.time() - start
        
        # Time multi-threaded generation (use 4 threads)
        start = time.time()
        multi_threaded = self.generator.generate_multiple_lines_threaded(
            app_name, total_lines, workers=4
        )
        multi_threaded_time = time.time() - start
        
        # Verify results are the same length
        self.assertEqual(len(single_threaded), len(multi_threaded))
        
        # Output performance comparison
        print(f"\nPerformance comparison for {total_lines} lines:")
        print(f"  Single-threaded: {single_threaded_time:.4f} seconds")
        print(f"  Multi-threaded (4 workers): {multi_threaded_time:.4f} seconds")
        print(f"  Speedup: {single_threaded_time/multi_threaded_time:.2f}x")
        
        # Multi-threaded should be faster (but we won't fail the test if it's not,
        # as performance can vary based on system load and other factors)
        if multi_threaded_time >= single_threaded_time:
            print("  Note: Multi-threaded was not faster than single-threaded. "
                  "This can happen on systems with few CPU cores or high load.")
    
    def test_threaded_generation_edge_cases(self):
        """Test edge cases for threaded generation."""
        app_name = 'apf-dsn-flow'
        
        # Test with very small number of lines
        lines = self.generator.generate_multiple_lines_threaded(app_name, 1, workers=4)
        self.assertEqual(len(lines), 1)
        
        # Test with number of lines not divisible by number of workers
        lines = self.generator.generate_multiple_lines_threaded(app_name, 7, workers=4)
        self.assertEqual(len(lines), 7)
        
        # Test with more workers than lines
        lines = self.generator.generate_multiple_lines_threaded(app_name, 3, workers=10)
        self.assertEqual(len(lines), 3)


if __name__ == "__main__":
    unittest.main()
