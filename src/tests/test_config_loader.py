#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Unit tests for the config_loader module.
"""

import os
import unittest
import tempfile
from unittest.mock import patch

import sys
import yaml

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from config_loader import ConfigLoader


class TestConfigLoader(unittest.TestCase):
    """
    Test cases for ConfigLoader class.
    """
    
    def setUp(self):
        """
        Set up test environment.
        """
        # Create a temporary config file for testing
        self.temp_dir = tempfile.TemporaryDirectory()
        self.config_path = os.path.join(self.temp_dir.name, 'test_config.yaml')
        
        # Sample configuration
        self.test_config = {
            'logs': [
                {'file_count': 100},
                {'file_size_after_compression': '50 MB'}
            ],
            'timestamp': {
                'from': '2025-01-01 00:00:00',
                'to': '2025-01-02 23:59:59',
                'format': '%Y-%m-%d %H:%M:%S,%f'
            },
            'foundation': {
                'test-app': {
                    'format': '__TIMESTAMP__ test [__UUID__] __VAR1__',
                    'path': 'test/logs',
                    'file_name': 'test_%y%m%d',
                    'file_extension': '.log.gz',
                    'log_line_use_rate': 0.8,
                    'variables': {
                        '__VAR1__': ['value1', 'value2']
                    }
                }
            }
        }
        
        # Write test config to file
        with open(self.config_path, 'w') as f:
            yaml.dump(self.test_config, f)
            
        # Create config loader
        self.config_loader = ConfigLoader(self.config_path)
    
    def tearDown(self):
        """
        Clean up after tests.
        """
        self.temp_dir.cleanup()
    
    def test_load_config(self):
        """
        Test loading configuration from file.
        """
        config = self.config_loader.config
        self.assertIsNotNone(config)
        self.assertIn('logs', config)
        self.assertIn('timestamp', config)
        self.assertIn('foundation', config)
    
    def test_get_log_config(self):
        """
        Test getting log configuration.
        """
        log_config = self.config_loader.get_log_config()
        self.assertEqual(len(log_config), 2)
        self.assertEqual(log_config[0]['file_count'], 100)
    
    def test_get_timestamp_config(self):
        """
        Test getting timestamp configuration.
        """
        timestamp_config = self.config_loader.get_timestamp_config()
        self.assertEqual(timestamp_config['from'], '2025-01-01 00:00:00')
        self.assertEqual(timestamp_config['to'], '2025-01-02 23:59:59')
        self.assertEqual(timestamp_config['format'], '%Y-%m-%d %H:%M:%S,%f')
    
    def test_get_foundation_config(self):
        """
        Test getting foundation configuration.
        """
        foundation_config = self.config_loader.get_foundation_config()
        self.assertIn('test-app', foundation_config)
    
    def test_get_log_format(self):
        """
        Test getting log format.
        """
        log_format = self.config_loader.get_log_format('test-app')
        self.assertEqual(log_format, '__TIMESTAMP__ test [__UUID__] __VAR1__')
        
        # Test non-existent app
        empty_format = self.config_loader.get_log_format('non-existent')
        self.assertEqual(empty_format, "")
    
    def test_get_log_path(self):
        """
        Test getting log path.
        """
        log_path = self.config_loader.get_log_path('test-app')
        self.assertEqual(log_path, 'test/logs')
    
    def test_get_file_name_pattern(self):
        """
        Test getting file name pattern.
        """
        pattern = self.config_loader.get_file_name_pattern('test-app')
        self.assertEqual(pattern, 'test_%y%m%d')
    
    def test_get_file_extension(self):
        """
        Test getting file extension.
        """
        extension = self.config_loader.get_file_extension('test-app')
        self.assertEqual(extension, '.log.gz')
    
    def test_get_log_line_use_rate(self):
        """
        Test getting log line use rate.
        """
        rate = self.config_loader.get_log_line_use_rate('test-app')
        self.assertEqual(rate, 0.8)
        
        # Test default value for non-existent app
        default_rate = self.config_loader.get_log_line_use_rate('non-existent')
        self.assertEqual(default_rate, 1.0)
    
    def test_get_variables(self):
        """
        Test getting variables.
        """
        variables = self.config_loader.get_variables('test-app')
        self.assertIn('__VAR1__', variables)
        self.assertEqual(variables['__VAR1__'], ['value1', 'value2'])
    
    def test_get_file_count(self):
        """
        Test getting file count.
        """
        file_count = self.config_loader.get_file_count()
        self.assertEqual(file_count, 100)
    
    def test_get_file_size_after_compression(self):
        """
        Test getting file size after compression.
        """
        file_size = self.config_loader.get_file_size_after_compression()
        self.assertEqual(file_size, '50 MB')
    
    def test_file_not_found(self):
        """
        Test handling of non-existent config file.
        """
        with self.assertRaises(FileNotFoundError):
            ConfigLoader('/path/to/non-existent/file.yaml')
    
    @patch('yaml.safe_load')
    def test_yaml_error(self, mock_safe_load):
        """
        Test handling of YAML parsing errors.
        """
        # Mock yaml.safe_load to raise YAMLError
        mock_safe_load.side_effect = yaml.YAMLError("Test YAML error")
        
        with self.assertRaises(ValueError):
            ConfigLoader(self.config_path)


if __name__ == '__main__':
    unittest.main()
