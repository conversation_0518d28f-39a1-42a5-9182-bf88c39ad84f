#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Log writer module.
This module is responsible for writing logs to files and compressing them.
"""

import gzip
import os
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Union

from src.config_loader import Config<PERSON>oader
from src.log_generator import LogGenerator


class LogWriter(LogGenerator):
    """
    Write log lines to files and compress them.
    """
    
    def __init__(self, config):
        """
        Initialize log writer with configuration.
        
        Args:
            config: ConfigLoader instance
        """
        self.config = config
        self.base_dir = ""
        self.compress = False
    
    def set_base_dir(self, base_dir: str):
        """
        Set base directory for log files.
        
        Args:
            base_dir: Base directory path
        """
        self.base_dir = base_dir
        
    def set_compress(self, compress: bool):
        """
        Set compression option.
        
        Args:
            compress: Whether to compress output files with gzip
        """
        self.compress = compress
    
    def _ensure_directory_exists(self, directory: str):
        """
        Ensure that the directory exists, create if not.
        
        Args:
            directory: Directory path to check/create
        """
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
    
    def _generate_file_name(self, app_name: str, timestamp: Optional[datetime] = None) -> str:
        """
        Generate file name based on pattern in configuration.
        
        Args:
            app_name: Name of the application
            timestamp: Optional timestamp for file name
        
        Returns:
            Generated file name
        """
        file_name_pattern = self.config.get_file_name_pattern(app_name)
        file_extension = self.config.get_file_extension(app_name)
        
        if timestamp is None:
            timestamp = datetime.now()
            
        # %y%m%d%H%M%S 形式で時間を置換
        file_name = timestamp.strftime(file_name_pattern)
        
        return f"{file_name}{file_extension}"
    
    def _get_log_file_path(self, app_name: str, timestamp: Optional[datetime] = None) -> str:
        """
        Get full path for log file.
        
        Args:
            app_name: Name of the application
            timestamp: Optional timestamp for file name
        
        Returns:
            Full path for log file
        """
        # configから相対パスを取得
        relative_path = self.config.get_log_path(app_name)
        file_name = self._generate_file_name(app_name, timestamp)
        
        # base_dirが設定されている場合、base_dirを起点にconfigのパスを使用する
        # パスが絶対パスの場合はそのまま使用
        if os.path.isabs(relative_path):
            full_path = relative_path
        elif self.base_dir:
            full_path = os.path.join(self.base_dir, relative_path)
        else:
            full_path = relative_path
            
        self._ensure_directory_exists(full_path)
        
        return os.path.join(full_path, file_name)
    
    def write_logs_to_file(self, app_name: str, log_lines: List[str], 
                          timestamp: Optional[datetime] = None,
                          progress_desc: Optional[str] = None) -> str:
        """
        Write log lines to a file, optionally compress it.
        
        Args:
            app_name: Name of the application
            log_lines: List of log lines to write
            timestamp: Optional timestamp for file name
        
        Returns:
            Path to the written file
        """
        base_file_path = self._get_log_file_path(app_name, timestamp)
        
        # ファイルパスから拡張子を処理
        if base_file_path.endswith('.gz') and not self.compress:
            # 圧縮しない場合は.gz拡張子を削除
            file_path = base_file_path[:-3]
        elif not base_file_path.endswith('.gz') and self.compress:
            # 圧縮する場合は.gz拡張子を追加
            file_path = f"{base_file_path}.gz"
        else:
            file_path = base_file_path
            
        # 進捗表示の設定
        from tqdm import tqdm
        disable_progress = progress_desc is None
        
        # 圧縮オプションに基づいて書き込み
        if self.compress:
            with gzip.open(file_path, 'wt', encoding='utf-8') as f:
                for line in tqdm(log_lines, desc=progress_desc, unit='lines', 
                               disable=disable_progress, leave=True):
                    f.write(f"{line}\n")
        else:
            with open(file_path, 'w', encoding='utf-8') as f:
                for line in tqdm(log_lines, desc=progress_desc, unit='lines',
                               disable=disable_progress, leave=True):
                    f.write(f"{line}\n")
                    
        return file_path
        
    def write_logs_in_chunks(self, app_name: str, log_generator, total_lines: int,
                                chunk_size: int = 10000, timestamp: Optional[datetime] = None) -> str:
        """
        Write logs to a file in chunks to reduce memory usage.
        Compression is done after writing to improve performance.

        Args:
            app_name: Name of the application
            log_generator: Generator that yields log lines
            total_lines: Total number of lines to be written (for progress tracking)
            chunk_size: Number of lines to process in each chunk
            timestamp: Optional timestamp for file name

        Returns:
            Path to the written file (compressed if compression is enabled)
        """
        base_file_path = self._get_log_file_path(app_name, timestamp)

        # Always write uncompressed first for better performance
        if base_file_path.endswith('.gz'):
            temp_file_path = base_file_path[:-3]  # Remove .gz extension
        else:
            temp_file_path = base_file_path

        from tqdm import tqdm

        # Create progress bar for writing
        pbar = tqdm(total=total_lines, desc='Writing logs', unit='lines')

        try:
            # Always write uncompressed first
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                self._write_chunks_to_file(f, log_generator, chunk_size, pbar)
        finally:
            pbar.close()

        # Compress after writing if compression is enabled
        if self.compress:
            final_file_path = f"{temp_file_path}.gz"
            print(f"  → Compressing file: {os.path.basename(temp_file_path)}")
            self._compress_file_fast(temp_file_path, final_file_path)
            # Remove the uncompressed file
            os.remove(temp_file_path)
            return final_file_path
        else:
            return temp_file_path
        
    def _write_chunks_to_file(self, file_obj, log_generator, chunk_size, progress_bar):
        """Helper method to write chunks to file with progress tracking."""
        while True:
            chunk = []
            try:
                # Get a chunk of lines
                for _ in range(chunk_size):
                    chunk.append(next(log_generator))

                # Write the chunk
                for line in chunk:
                    file_obj.write(f"{line}\n")

                # Update progress for the whole chunk at once
                progress_bar.update(len(chunk))

                # Clear the chunk to free memory
                chunk.clear()

            except StopIteration:
                # Write any remaining lines
                if chunk:
                    for line in chunk:
                        file_obj.write(f"{line}\n")
                    progress_bar.update(len(chunk))
                break

    def _compress_file_fast(self, input_file: str, output_file: str, chunk_size: int = 1024*1024):
        """
        Compress file using gzip with optimized settings for speed.

        Args:
            input_file: Path to input file
            output_file: Path to output compressed file
            chunk_size: Size of chunks to read/write (default 1MB)
        """
        # Get file size for progress tracking
        file_size = os.path.getsize(input_file)

        from tqdm import tqdm

        with open(input_file, 'rb') as f_in:
            # Use compression level 6 for good balance of speed and compression
            with gzip.open(output_file, 'wb', compresslevel=6) as f_out:
                with tqdm(total=file_size, desc='Compressing', unit='B', unit_scale=True) as pbar:
                    while True:
                        chunk = f_in.read(chunk_size)
                        if not chunk:
                            break
                        f_out.write(chunk)
                        pbar.update(len(chunk))
    
    def estimate_lines_for_size(self, app_name: str, target_size_mb: int,
                               compress: bool = False, sample_lines: int = 20000) -> int:
        """
        Estimate how many log lines are needed to reach the target file size.

        Args:
            app_name: Name of the application
            target_size_mb: Target file size in MB (after compression if compress=True)
            compress: Whether to estimate for compressed file size
            sample_lines: Number of lines to use for estimation

        Returns:
            Estimated number of lines
        """
        from src.log_generator import LogGenerator

        # Tăng số lượng sample để tính toán chính xác hơn
        generator = LogGenerator(self.config)
        sample = generator.generate_multiple_lines(app_name, sample_lines)

        # Đo kích thước sample
        temp_file = os.path.join("/tmp", f"log_sample_{int(time.time())}")
        if compress:
            temp_file += ".gz"
            with gzip.open(temp_file, 'wt', encoding='utf-8') as f:
                for line in sample:
                    f.write(f"{line}\n")
        else:
            with open(temp_file, 'w', encoding='utf-8') as f:
                for line in sample:
                    f.write(f"{line}\n")

        # Lấy kích thước file
        sample_size = os.path.getsize(temp_file)
        os.unlink(temp_file)  # Xóa file tạm

        # Tính toán số dòng cần thiết
        target_size_bytes = target_size_mb * 1024 * 1024
        avg_line_size = sample_size / sample_lines

        # Sử dụng correction factor bảo thủ hơn để đảm bảo không vượt quá target
        if compress:
            correction_factor = 0.85  # Giảm 15% để đảm bảo không vượt quá
        else:
            correction_factor = 0.88  # Giảm 12% để đảm bảo không vượt quá

        estimated_lines = int((target_size_bytes / avg_line_size) * correction_factor)

        # Đảm bảo số dòng tối thiểu hợp lý
        if target_size_mb > 50:
            min_lines = 50000
        else:
            min_lines = 10000

        return max(min_lines, estimated_lines)


if __name__ == "__main__":
    # テスト用コード
    from config_loader import ConfigLoader
    from log_generator import LogGenerator
    
    config = ConfigLoader("../config.yaml")
    generator = LogGenerator(config)
    writer = LogWriter(config)
    
    # ベースディレクトリを設定
    writer.set_base_dir("../output")
    
    # 10行のログを生成して書き込み
    log_lines = generator.generate_multiple_lines("apf-dsn-flow", 10)
    file_path = writer.write_logs_to_file("apf-dsn-flow", log_lines)
    
    print(f"ログファイルが保存されました: {file_path}")
    
    # 100MBのファイルサイズに必要な行数を推定
    target_size_mb = 100
    estimated_lines = writer.estimate_lines_for_size("apf-dsn-flow", target_size_mb)
    print(f"{target_size_mb}MB のファイルサイズに必要な推定行数: {estimated_lines}")
