#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Command line interface for log generator.
This module provides a CLI for generating log files based on configuration.
"""

import argparse
import os
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union

# Import using absolute imports
from src.config_loader import ConfigLoader
from src.log_generator import LogGenerator
from src.log_writer import LogWriter


def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description='Generate log files based on configuration')
    
    parser.add_argument('--config', type=str, default='config.yaml',
                        help='Path to configuration file (default: config.yaml)')
    parser.add_argument('--output-dir', type=str, default='output',
                        help='Base output directory for log files (default: output)')
    parser.add_argument('--app-name', type=str, default=None,
                        help='Application name in configuration (default: all applications)')
    parser.add_argument('--file-count', type=int,
                        help='Override file count from configuration')
    parser.add_argument('--size-mb', type=int,
                        help='Override file size (MB) from configuration')
    parser.add_argument('--dry-run', action='store_true',
                        help='Perform a dry run without writing files')
    parser.add_argument('-z', '--compress', action='store_true',
                        help='Compress output log files using gzip')
    parser.add_argument('--threads', type=int, default=None,
                        help='Number of worker threads to use for log generation. '
                             'If not specified, it will be automatically determined.')
    parser.add_argument('--chunk-size', type=int, default=10000,
                        help='Number of lines to process in each chunk (default: 10000)')
    
    return parser.parse_args()


def parse_size_string(size_str: str) -> int:
    """
    Parse size string like '100 MB' to MB integer.
    
    Args:
        size_str: Size string to parse
    
    Returns:
        Size in MB
    """
    size_str = size_str.strip()
    parts = size_str.split(' ')
    
    if len(parts) != 2:
        return 0
        
    try:
        size = float(parts[0])
        unit = parts[1].upper()
        
        if unit == 'MB':
            return int(size)
        elif unit == 'GB':
            return int(size * 1024)
        elif unit == 'KB':
            return max(1, int(size / 1024))
        else:
            return 0
    except ValueError:
        return 0


def generate_logs_for_app(config: ConfigLoader, output_dir: str, app_name: str,
                       file_count: int = None, size_mb: int = None, compress: bool = False,
                       dry_run: bool = False, threads: int = None, chunk_size: int = 10000):
    """
    Generate log files for a specific application.
    
    Args:
        config: ConfigLoader instance
        output_dir: Base output directory for log files
        app_name: Application name in configuration
        file_count: Override file count from configuration
        size_mb: Override file size (MB) from configuration
        compress: Whether to compress output files with gzip
        dry_run: Perform a dry run without writing files
        threads: Number of worker threads to use
        chunk_size: Number of lines to process in each chunk
    """
    # 行数と出力ファイル数を取得
    foundation_config = config.get_foundation_config()
    print(f"Debug - Processing app: {app_name}")
    print(f"Debug - Available apps in foundation: {list(foundation_config.keys())}")
    app_config = foundation_config.get(app_name, {})
    
    # アプリ固有の設定を取得 (デフォルト là tham số truyền vào hoặc từ config)
    app_file_count = file_count if file_count is not None else config.get_file_count(app_name)
        
    if size_mb is None:
        size_str = config.get_file_size_after_compression()
        size_mb = parse_size_string(size_str)
    
    print(f"\n{'='*50}")
    print(f"Generating logs for application: {app_name}")
    print(f"- Files to generate: {app_file_count}")
    print(f"- Target file size: {size_mb} MB {'(compressed)' if compress else ''}")
    print(f"- Compression: {'enabled' if compress else 'disabled'}")
    
    if dry_run:
        print("\nDRY RUN - No files will be written")
    
    # ログ生成と書き込みの設定
    generator = LogGenerator(config)
    writer = LogWriter(config)
    writer.set_base_dir(output_dir)
    writer.set_compress(compress)
    
    # マルチスレッドを使用するかどうかを決定
    # デフォルトでマルチスレッドを使用、明示的に1が指定された場合のみシングルスレッド
    use_threading = threads != 1
    
    # 進捗表示のための設定
    from tqdm import tqdm
    
    for i in tqdm(range(app_file_count), desc=f"{app_name} files", unit="file"):
        # ログ行数を推定または取得
        if size_mb is not None and size_mb > 0:
            lines = writer.estimate_lines_for_size(app_name, size_mb, compress)
            tqdm.write(f"  File {i+1}/{app_file_count}: Generating ~{lines:,} lines for {size_mb}MB file")
        else:
            lines = config.get_line_count()
            tqdm.write(f"  File {i+1}/{app_file_count}: Generating {lines:,} lines")
        
        # ログ生成の進捗表示用の説明
        progress_desc = f"{app_name} - File {i+1}/{app_file_count}"
        
        # ログを生成してファイルに書き込み
        start_time = time.time()
        
        if not dry_run:
            # 実際にファイルに書き込む
            if use_threading:
                # マルチスレッドで生成してチャンクごとに書き込み
                log_generator = generator._generate_log_lines_threaded(
                    app_name, lines, max_workers=threads
                )
                file_path = writer.write_logs_in_chunks(
                    app_name, 
                    log_generator,
                    total_lines=lines,
                    chunk_size=chunk_size
                )
            else:
                # シングルスレッドで生成してチャンクごとに書き込み
                log_generator = generator._generate_log_lines(app_name, lines)
                file_path = writer.write_logs_in_chunks(
                    app_name,
                    log_generator,
                    total_lines=lines,
                    chunk_size=chunk_size
                )
            
            write_time = time.time() - start_time
            tqdm.write(f"  → Generated file: {file_path}")
            tqdm.write(f"  → Total time: {write_time:.2f} seconds")
        else:
            # ドライラン: ログを生成するがファイルには書き込まない
            gen_start = time.time()
            if use_threading:
                log_lines = list(tqdm(
                    generator._generate_log_lines_threaded(app_name, lines, max_workers=threads),
                    total=lines,
                    desc=progress_desc,
                    unit=" lines"
                ))
            else:
                log_lines = list(tqdm(
                    generator._generate_log_lines(app_name, lines),
                    total=lines,
                    desc=progress_desc,
                    unit=" lines"
                ))
            
            gen_time = time.time() - gen_start
            tqdm.write(f"[Dry run] Would generate {len(log_lines):,} lines for {app_name}")
            tqdm.write(f"  → Generation time: {gen_time:.2f} seconds ({len(log_lines)/gen_time:,.0f} lines/sec)")
            
            # メモリを解放
            del log_lines

def generate_logs(config_path: str, output_dir: str, app_name: str = None,
                 file_count: int = None, size_mb: int = None, compress: bool = False,
                 dry_run: bool = False, threads: int = None, chunk_size: int = 10000):
    """
    Generate log files based on configuration.
    
    Args:
        config_path: Path to configuration file
        output_dir: Base output directory for log files
        app_name: Application name in configuration (None for all applications)
        file_count: Override file count from configuration
        size_mb: Override file size (MB) from configuration
        compress: Whether to compress output files with gzip
        dry_run: Perform a dry run without writing files
        threads: Number of worker threads to use
        chunk_size: Number of lines to process in each chunk
    """
    # 設定ファイルをロード
    config = ConfigLoader(config_path)
    
    # 出力ディレクトリを作成
    os.makedirs(output_dir, exist_ok=True)
    
    # アプリケーションを取得
    foundation_config = config.get_foundation_config()
    print(f"Debug - Foundation config: {foundation_config}")
    print(f"Debug - Foundation config type: {type(foundation_config)}")
    print(f"Debug - Foundation config keys: {list(foundation_config.keys())}")
    
    if app_name:
        # 指定されたアプリケーションのみを処理
        if app_name not in foundation_config:
            print(f"Error: Application '{app_name}' not found in configuration")
            return
        app_names = [app_name]
    else:
        # すべてのアプリケーションを処理
        app_names = list(foundation_config.keys())
    
    print(f"Starting log generation for {len(app_names)} application(s): {app_names}")
    
    # 各アプリケーションに対してログを生成
    for app in app_names:
        generate_logs_for_app(
            config=config,
            output_dir=output_dir,
            app_name=app,
            file_count=file_count,
            size_mb=size_mb,
            compress=compress,
            dry_run=dry_run,
            threads=threads,
            chunk_size=chunk_size
        )


def main():
    """
    Main entry point.
    """
    args = parse_args()
    
    # 設定ファイルパスを解決
    if not os.path.isabs(args.config):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, '..', args.config)
        config_path = os.path.normpath(config_path)
    else:
        config_path = args.config
    
    # 出力ディレクトリを解決
    if not os.path.isabs(args.output_dir):
        script_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(script_dir, '..', args.output_dir)
        output_dir = os.path.normpath(output_dir)
    else:
        output_dir = args.output_dir
    
    # 出力ディレクトリを作成
    os.makedirs(output_dir, exist_ok=True)
    
    # スレッド数が指定されていない場合は、CPUコア数に基づいて自動決定
    if args.threads is not None and args.threads < 1:
        print("Warning: Thread count must be at least 1. Using default value.")
        args.threads = None

    # デフォルトでマルチスレッドを使用
    if args.threads is None:
        args.threads = min(32, (os.cpu_count() or 1) + 4)

    print(f"Using {args.threads} worker threads (threading: {'enabled' if args.threads != 1 else 'disabled'})")
    
    # ログ生成を実行
    start_time = time.time()
    generate_logs(
        config_path=config_path,
        output_dir=output_dir,
        app_name=args.app_name,
        file_count=args.file_count,
        size_mb=args.size_mb,
        compress=args.compress,
        dry_run=args.dry_run,
        threads=args.threads,
        chunk_size=args.chunk_size
    )
    
    total_time = time.time() - start_time
    print(f"\nTotal execution time: {total_time:.2f} seconds")


if __name__ == "__main__":
    main()
