# Kế hoạch tăng tốc tạo log bằng đa luồng

## Notes
- <PERSON><PERSON>n trả lời người dùng bằng tiếng Việt, comment trong code bằng tiếng Anh.
- Nhắc nhở tuân theo các quy tắc trong thư mục `docs` với mỗi lần trả lời.
- Tách mock data và source code, giữ codebase sạch sẽ, loại bỏ mã thừa.
- Đ<PERSON>m bảo tính nhất quán trong toàn bộ mã nguồn.
- Cần triển khai đa luồng (ThreadPoolExecutor) để tăng tốc tạo log, đồng thầm đảm bảo an toàn luồng cho bộ sinh VIN và các tài nguyên chung.
- Áp dụng best-practice Python (concurrent.futures, context manager, lock).
- Cung cấp benchmark so sánh tốc độ trước và sau.
- Đã triển khai đa luồng ThreadPoolExecutor, đảm bảo thread safety cho VIN và timestamp.
- Đ<PERSON> cập nhật CLI hỗ trợ `--threads` và thêm unit tests đa luồng.
- Cần thêm progress bar (`tqdm`) để hiển thị tiến trình.
- Tối ưu bộ nhớ, ghi file theo từng chunk, tránh lưu toàn bộ list log trong RAM.
- Đã cập nhật CLI: mặc định xử lý tất cả application khi không truyền --app-name và thêm debug output; Makefile thêm PYTHONPATH và chuyển sang dùng python3.
- Đã thêm `src/__init__.py` biến `src` thành Python package; refactor import sang `src.*` trong các module.
- Đã refactor datetime import (from datetime import datetime, timedelta), cần xoá hết tham chiếu `datetime.datetime` còn sót.
- Đã refactor datetime trong `log_writer.py` và xoá toàn bộ `datetime.datetime`.
- Đã sửa import sai trong `estimate_lines_for_size` (`from src.log_generator import LogGenerator`).
- Người dùng yêu cầu chọn ngẫu nhiên 1 giá trị `__BODY__` khi danh sách có nhiều mục, cần đảm bảo logic trong LogGenerator.
- Đã cập nhật `_replace_variables` để chọn ngẫu nhiên `__BODY__`; 15 unit tests thất bại do MagicMock cần sửa.
- Nguyên nhân: patch trong tests vẫn dùng `log_generator.LogGenerator`; cần đổi thành `src.log_generator.LogGenerator` và mock config phải cung cấp timestamp string hợp lệ.
- Đã cập nhật `test_log_writer.py`: sửa patch path và thêm mock `get_timestamp_config`; chuẩn bị chạy lại pytest.
- Đã cập nhật đường patch trong tests sang `src.log_generator.LogGenerator`.
- Đã thêm mock `get_timestamp_config` trả về dict hợp lệ.
- Đã chạy lại pytest, **còn 4 test thất bại** liên quan tới ConfigLoader (foundation list), và tham số `max_workers` trong `generate_multiple_lines_threaded`.
- Phát sinh lỗi nghiêm trọng trong `log_generator.py` do merge sai; cấu trúc class bị hỏng, indent sai, nhiều lint errors; cần refactor triệt để.
- Đã sửa lại indent các method trong `log_generator.py`, khắc phục một số lint errors; cần chạy pytest để kiểm tra.
- Đã cập nhật `test_log_generator_threaded.py`: đổi `max_workers` thành `workers` để phù hợp signature hiện tại.
- Đã cập nhật `test_config_loader.py` để chuyển foundation sang dict mapping, kỳ vọng khắc phục lỗi AttributeError.
- Đã cập nhật CLI: mặc định xử lý tất cả application khi không truyền --app-name và thêm debug output; Makefile thêm PYTHONPATH và chuyển sang dùng python3.
- Đã thêm `src/__init__.py` biến `src` thành Python package; refactor import sang `src.*` trong các module.
- Đã refactor datetime import (from datetime import datetime, timedelta), cần xoá hết tham chiếu `datetime.datetime` còn sót.
- Đã refactor datetime trong `log_writer.py` và xoà toàn bộ `datetime.datetime`.
- Đã sửa import sai trong `estimate_lines_for_size` (`from src.log_generator import LogGenerator`).
- Người dùng yêu cầu chọn ngẫu nhiên 1 giá trị `__BODY__` khi danh sách có nhiều mục, cần đảm bảo logic trong LogGenerator.
- Đã cập nhật `_replace_variables` để chọn ngẫu nhiên `__BODY__`; 15 unit tests thất bại do MagicMock cần sửa.
- Nguyên nhân: patch trong tests vẫn dùng `log_generator.LogGenerator`; cần đổi thành `src.log_generator.LogGenerator` và mock config phải cung cấp timestamp string hợp lệ.
- Đã cập nhật `test_log_writer.py`: sửa patch path và thêm mock `get_timestamp_config`; chuẩn bị chạy lại pytest.
- Đã cập nhật đường patch trong tests sang `src.log_generator.LogGenerator`.
- Đã thêm mock `get_timestamp_config` trả về dict hợp lệ.
- Đã chạy lại pytest, **còn 4 test thất bại** liên quan tới ConfigLoader (foundation list), và tham số `max_workers` trong `generate_multiple_lines_threaded`.

## Task List
- [x] Đọc và rà soát code hiện tại của `log_generator.py` và `log_writer.py`.
- [x] Thiết kế kiến trúc thread-safe cho LogGenerator (xác định và bảo vệ trạng thái chung bằng `threading.Lock` hoặc biến thread-local).
- [x] Thêm phương thức `generate_multiple_lines_threaded(app_name, total_lines, workers)` dùng `ThreadPoolExecutor`.
- [x] Đảm bảo bộ sinh VIN tạo giá trị duy nhất giữa các luồng (counter nguyên tử).
- [ ] (Tuỳ chọn) Tối ưu LogWriter để ghi file song song nếu cần.
- [x] Cập nhật CLI: thêm tham số `--threads` cho người dùng chỉ định số luồng.
- [x] Viết unit test cho chức năng đa luồng (đủ số dòng, VIN không trùng, hiệu năng).
- [ ] Benchmark hiệu năng, lưu kết quả vào `docs/perf.md`.
- [ ] Cập nhật tài liệu, đảm bảo tuân thủ quy tắc trong `docs`.
- [ ] Làm sạch code, loại bỏ phần thừa, đảm bảo nhất quán.
- [ ] Thêm progress bar (`tqdm`) vào CLI/LogWriter để hiển thị tiến trình.
- [ ] Tối ưu bộ nhớ: sinh & ghi log theo chunk, giảm peak RAM.
- [ ] Điều chỉnh thuật toán ước tính để đạt chính xác 100MB, tránh vượt 139MB.
- [x] Sửa Makefile sử dụng python3/pip3.
- [x] Tạo file `src/__init__.py` để khai báo package.
- [x] Chuyển import sang `src.*` trong cli, log_generator, log_writer.
- [x] Sửa lỗi datetime & lint trong log_generator.
- [x] Sửa lỗi datetime trong log_writer.
- [x] Sửa import sai trong `estimate_lines_for_size` (`from src.log_generator import LogGenerator`).
- [x] Xác nhận `make run-all` chạy thành công.
- [x] Đảm bảo random `__BODY__` khi list trong LogGenerator.
- [ ] Viết unit test cho random `__BODY__`.
- [ ] Sửa unit tests bị lỗi MagicMock sau cập nhật __BODY__.
  - [x] Cập nhật đường patch trong tests sang `src.log_generator.LogGenerator`.
  - [x] Thêm mock `get_timestamp_config` trả về dict hợp lệ.
  - [ ] Chạy lại pytest, đảm bảo 0 failed.
  - [ ] Sửa ConfigLoader để hỗ trợ foundation dưới dạng list → dict mapping, khắc phục các phương thức `get_log_format`, `get_log_path`, ...
  - [ ] Thêm/điều chỉnh unit tests cho ConfigLoader nếu cần.
  - [x] Điều chỉnh `test_config_loader` để sử dụng foundation dict mapping, khắc phục lỗi AttributeError.
  - [ ] Sửa chữ ký `generate_multiple_lines_threaded` để nhận `max_workers` (duy trì tương thích ngược), cập nhật code & tests.
  - [x] Cập nhật tests sử dụng tham số `workers` cho `generate_multiple_lines_threaded`, giữ nguyên signature.
  - [ ] Sửa generate_logs_for_app để gọi LogGenerator đúng, khắc phục lỗi MagicMock trong test_cli.
- [ ] Tối ưu bộ nhớ: sinh & ghi log theo chunk, giảm peak RAM.
- [ ] Điều chỉnh thuật toán ước tính để đạt chính xác 100MB, tránh vượt 139MB.
- [ ] Refactor `log_generator.py`: sửa indent, đặt phương thức đúng vị trí, xoá code thừa, đảm bảo pass lint & unit tests.
  - [x] Sửa indent các method, đảm bảo thuộc về class `LogGenerator`.
- [ ] Chạy `pytest` sau refactor, sửa lỗi còn lại đến khi 0 failed.
- [ ] Sửa CLI.parse_args: đặt mặc định `app_name` = 'apf-dsn-flow' và đảm bảo tests CLI pass.

## Current Goal
Sửa CLI & max_workers để toàn bộ test pass.