# Log Generator - Windows Build Script (PowerShell)
# Tạo executable cho Windows từ dự án Python

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   LOG GENERATOR - WINDOWS BUILD SCRIPT" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Script này sẽ tạo file executable cho Windows" -ForegroundColor Yellow
Write-Host ""

# Kiểm tra Python
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python đã được cài đặt: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ Python chưa được cài đặt!" -ForegroundColor Red
    Write-Host "📝 Hướng dẫn:" -ForegroundColor Yellow
    Write-Host "1. Tải Python từ: https://python.org" -ForegroundColor White
    Write-Host "2. Cài Python với tùy chọn 'Add to PATH'" -ForegroundColor White
    Write-Host "3. Chạy lại script này" -ForegroundColor White
    Read-Host "Nhấn Enter để thoát"
    exit 1
}

Write-Host ""
Write-Host "🔨 Bắt đầu build process..." -ForegroundColor Yellow
Write-Host ""

# Chạy Python build script
try {
    python build_windows.py
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "🎉 Build hoàn thành!" -ForegroundColor Green
        Write-Host "📁 Kiểm tra thư mục log-generator-windows-portable" -ForegroundColor Cyan
        Write-Host "📦 Hoặc file ZIP log-generator-windows-portable.zip" -ForegroundColor Cyan
    } else {
        throw "Build failed"
    }
} catch {
    Write-Host ""
    Write-Host "❌ Build thất bại!" -ForegroundColor Red
    Write-Host "📝 Kiểm tra lỗi ở trên và thử lại" -ForegroundColor Yellow
    Read-Host "Nhấn Enter để thoát"
    exit 1
}

Write-Host ""
Read-Host "Nhấn Enter để thoát"
