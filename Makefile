# Default target: run all foundations with 8 threads
all: run-all

# Install dependencies
setup:
	pip install -r requirements.txt

# Run all foundations
run-all:
	@echo "Starting log generation for all foundations..."
	PYTHONPATH=${PWD} python3 -m src.cli --config config.yaml --output-dir output --threads 8
	@echo "Done."

# Run specific foundation (e.g., make run-foundation FOUNDATION=apf-dsn-flow)
run-foundation:
	@if [ -z "$(FOUNDATION)" ]; then \
		echo "Error: FOUNDATION variable is not set. Usage: make run-foundation FOUNDATION=app-name"; \
		exit 1; \
	fi
	@echo "Starting log generation for foundation: $(FOUNDATION)"
	PYTHONPATH=${PWD} python3 -m src.cli --config config.yaml --output-dir output --app-name $(FOUNDATION) --threads 8
	@echo "Done."

# Dry run to test without writing files
dry-run:
	@echo "Running dry run (no files will be written)..."
	PYTHONPATH=${PWD} python3 -m src.cli --config config.yaml --output-dir output --threads 8 --dry-run

# Test performance with different configurations
test-performance:
	@echo "Running performance tests..."
	PYTHONPATH=${PWD} python3 test_performance.py

# Run with compression enabled
run-compressed:
	@echo "Starting log generation with compression..."
	PYTHONPATH=${PWD} python3 -m src.cli --config config.yaml --output-dir output --threads 8 --compress
	@echo "Done."

# Run single threaded for comparison
run-single:
	@echo "Starting log generation with single thread..."
	PYTHONPATH=${PWD} python3 -m src.cli --config config.yaml --output-dir output --threads 1
	@echo "Done."

# Clean generated files
clean:
	@echo "Cleaning output directory..."
	rm -rf output/*
	@echo "Done."

.PHONY: all setup run-all run-foundation dry-run test-performance run-compressed run-single clean
