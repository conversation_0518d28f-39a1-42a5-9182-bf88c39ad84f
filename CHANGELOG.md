# Changelog

All notable changes to this project will be documented in this file.

## [1.0.0] - 2025-06-18

### Added
- VIN CSV generation với support cho Internal VIN và VIN data
- Cross-platform support với Python script và Batch file cho Windows
- Multi-threading để tăng tốc độ log generation
- Sequential timestamp generation để mô phỏng log thực tế
- File size estimation algorithm để đạt target size chính xác
- Support cho NULL values trong CSV
- Progress tracking với tqdm
- Docker support
- Conda environment support

### Changed
- InternalVin từ string với leading zeros thành integer type
- VIN format từ có leading zeros thành không có leading zeros (VIN + số)
- Timestamp generation từ random thành sequential với increments nhỏ
- File structure được tổ chức lại với VIN CSV generator riêng biệt

### Fixed
- CSV generation với đúng data types
- NULL values hiển thị đúng trong CSV
- Cross-platform compatibility issues
- Memory efficiency cho large datasets

### Technical Details
- **VIN Range**: Configurable từ `internal_vin_start` đến `internal_vin_end`
- **CSV Format**: InternalVin (int), <PERSON> (string), RegisteredCountry, PfDistinction, CvServiceId (NULL), EndDateTime (NULL)
- **Performance**: ~990K records trong ~2 phút, log generation ~53 giây cho 2 foundations
- **File Sizes**: Target 100MB, actual output 87-109MB
- **Threading**: Configurable số threads, default 8

### Dependencies
- PyYAML>=6.0
- tqdm>=4.64.0
- Python>=3.8
