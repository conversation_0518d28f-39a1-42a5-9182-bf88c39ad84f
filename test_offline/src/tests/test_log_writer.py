#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Unit tests for the log_writer module.
"""

import os
import gzip
import shutil
import unittest
import tempfile
import datetime
from unittest.mock import patch, MagicMock

import sys

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from log_writer import LogWriter


class TestLogWriter(unittest.TestCase):
    """
    Test cases for LogWriter class.
    """
    
    def setUp(self):
        """
        Set up test environment.
        """
        # Create temp directory
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Mock config
        self.mock_config = MagicMock()
        self.mock_config.get_file_name_pattern.return_value = "test_%y%m%d"
        self.mock_config.get_file_extension.return_value = ".log.gz"
        self.mock_config.get_log_path.return_value = "logs/test"
        
        # Mock timestamp config
        self.mock_config.get_timestamp_config.return_value = {
            'format': "%Y-%m-%d %H:%M:%S",
            'from': "2025-01-01 00:00:00",
            'to': "2025-01-02 00:00:00"
        }
        
        # Create writer
        self.writer = LogWriter(self.mock_config)
        self.writer.set_base_dir(self.temp_dir.name)
        # Default to not compressed
        self.writer.set_compress(False)
    
    def tearDown(self):
        """
        Clean up after tests.
        """
        self.temp_dir.cleanup()
    
    def test_set_base_dir(self):
        """
        Test setting base directory.
        """
        self.writer.set_base_dir("/tmp/test")
        self.assertEqual(self.writer.base_dir, "/tmp/test")
        
    def test_set_compress(self):
        """
        Test setting compression option.
        """
        self.writer.set_compress(True)
        self.assertTrue(self.writer.compress)
        
        self.writer.set_compress(False)
        self.assertFalse(self.writer.compress)
    
    def test_ensure_directory_exists(self):
        """
        Test ensuring directory exists.
        """
        test_dir = os.path.join(self.temp_dir.name, "test_dir")
        self.assertFalse(os.path.exists(test_dir))
        
        self.writer._ensure_directory_exists(test_dir)
        self.assertTrue(os.path.exists(test_dir))
    
    def test_generate_file_name(self):
        """
        Test generating file name.
        """
        app_name = "test-app"
        timestamp = datetime.datetime(2025, 1, 1, 12, 30, 45)
        
        file_name = self.writer._generate_file_name(app_name, timestamp)
        
        # Check that the filename matches the pattern with timestamp
        self.assertEqual(file_name, "test_250101.log.gz")
    
    def test_generate_file_name_current_time(self):
        """
        Test generating file name with current time.
        """
        app_name = "test-app"
        
        file_name = self.writer._generate_file_name(app_name)
        
        # Check that the filename matches the pattern
        self.assertRegex(file_name, r"test_\d{6}\.log\.gz")
    
    def test_get_log_file_path(self):
        """
        Test getting log file path.
        """
        app_name = "test-app"
        timestamp = datetime.datetime(2025, 1, 1, 12, 30, 45)
        
        file_path = self.writer._get_log_file_path(app_name, timestamp)
        
        # Check that the path is correct
        expected_path = os.path.join(self.temp_dir.name, "logs/test/test_250101.log.gz")
        self.assertEqual(file_path, expected_path)
        
        # Check that the directory was created
        self.assertTrue(os.path.exists(os.path.dirname(file_path)))
    
    def test_write_logs_to_file_gzip(self):
        """
        Test writing logs to gzipped file with compress flag.
        """
        app_name = "test-app"
        timestamp = datetime.datetime(2025, 1, 1, 12, 30, 45)
        log_lines = ["line1", "line2", "line3"]
        
        # Enable compression
        self.writer.set_compress(True)
        
        file_path = self.writer.write_logs_to_file(app_name, log_lines, timestamp)
        
        # Check that the file exists
        self.assertTrue(os.path.exists(file_path))
        
        # Check that file is gzipped
        self.assertTrue(file_path.endswith(".gz"))
        
        # Check contents
        with gzip.open(file_path, 'rt', encoding='utf-8') as f:
            content = f.read().strip().split('\n')
            self.assertEqual(content, log_lines)
    
    def test_write_logs_to_file_plain(self):
        """
        Test writing logs to plain text file.
        """
        app_name = "test-app"
        timestamp = datetime.datetime(2025, 1, 1, 12, 30, 45)
        log_lines = ["line1", "line2", "line3"]
        
        # Mock config to return plain text extension
        self.mock_config.get_file_extension.return_value = ".log"
        
        # Make sure compression is disabled
        self.writer.set_compress(False)
        
        file_path = self.writer.write_logs_to_file(app_name, log_lines, timestamp)
        
        # Check that the file exists
        self.assertTrue(os.path.exists(file_path))
        
        # Check that file is not gzipped
        self.assertTrue(file_path.endswith(".log"))
        self.assertFalse(file_path.endswith(".gz"))
        
        # Check contents
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip().split('\n')
            self.assertEqual(content, log_lines)
            
    def test_write_logs_with_extension_override(self):
        """
        Test that compression setting overrides file extension.
        """
        app_name = "test-app"
        timestamp = datetime.datetime(2025, 1, 1, 12, 30, 45)
        log_lines = ["line1", "line2", "line3"]
        
        # Test case 1: Config has .gz but compress=False
        self.mock_config.get_file_extension.return_value = ".log.gz"
        self.writer.set_compress(False)
        
        file_path = self.writer.write_logs_to_file(app_name, log_lines, timestamp)
        
        # Check that file is NOT gzipped despite .gz extension in config
        self.assertFalse(file_path.endswith(".gz"))
        self.assertTrue(file_path.endswith(".log"))
        
        # Check contents as plain text
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip().split('\n')
            self.assertEqual(content, log_lines)
        
        # Test case 2: Config has no .gz but compress=True
        self.mock_config.get_file_extension.return_value = ".log"
        self.writer.set_compress(True)
        
        file_path = self.writer.write_logs_to_file(app_name, log_lines, timestamp)
        
    def test_write_logs_in_chunks_plain_text(self):
        """Test writing logs in chunks to plain text file."""
        app_name = "test-app"
        
        # Create a generator that yields log lines
        def line_generator():
            for i in range(1000):
                yield f"Test line {i+1}"
        
        # Configure for plain text
        self.mock_config.get_file_extension.return_value = ".log"
        self.writer.set_compress(False)
        
        file_path = self.writer.write_logs_in_chunks(
            app_name, 
            line_generator(),
            total_lines=1000,
            chunk_size=100
        )
        
        # Verify file was created
        self.assertTrue(os.path.exists(file_path))
        self.assertTrue(file_path.endswith(".log"))
        
        # Verify content
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.read().splitlines()
            
        self.assertEqual(len(lines), 1000)
        self.assertEqual(lines[0], "Test line 1")
        self.assertEqual(lines[999], "Test line 1000")
    
    def test_write_logs_in_chunks_compressed(self):
        """Test writing logs in chunks to compressed file."""
        app_name = "test-app"
        
        # Create a generator that yields log lines
        def line_generator():
            for i in range(1000):
                yield f"Test line {i+1}"
        
        # Configure for compressed output
        self.mock_config.get_file_extension.return_value = ".log.gz"
        self.writer.set_compress(True)
        
        file_path = self.writer.write_logs_in_chunks(
            app_name, 
            line_generator(),
            total_lines=1000,
            chunk_size=100
        )
        
        # Verify file was created and is compressed
        self.assertTrue(os.path.exists(file_path))
        self.assertTrue(file_path.endswith(".log.gz"))
        
        # Verify content by reading the compressed file
        with gzip.open(file_path, 'rt', encoding='utf-8') as f:
            lines = f.read().splitlines()
            
        self.assertEqual(len(lines), 1000)
        self.assertEqual(lines[0], "Test line 1")
        self.assertEqual(lines[999], "Test line 1000")
    
    def test_write_logs_in_chunks_with_progress(self):
        """Test that progress tracking works with chunked writing."""
        app_name = "test-app"
        
        # Create a generator that yields log lines
        def line_generator():
            for i in range(100):
                yield f"Test line {i+1}"
        
        # Configure for plain text
        self.mock_config.get_file_extension.return_value = ".log"
        self.writer.set_compress(False)
        
        # Patch tqdm to capture progress updates
        with patch('tqdm.tqdm') as mock_tqdm:
            # Create a mock progress bar
            mock_pbar = MagicMock()
            mock_tqdm.return_value = mock_pbar
            
            # Create a real generator to avoid exhausting it
            test_generator = line_generator()
            
            # Call the method with our test generator
            file_path = self.writer.write_logs_in_chunks(
                app_name, 
                test_generator,
                total_lines=100,
                chunk_size=10
            )
            
            # Verify progress bar was created with correct parameters
            mock_tqdm.assert_called_once()
            call_args, call_kwargs = mock_tqdm.call_args
            self.assertEqual(call_kwargs['total'], 100)
            self.assertEqual(call_kwargs['desc'], 'Writing logs')
            self.assertEqual(call_kwargs['unit'], 'lines')
            
            # Verify close was called on the progress bar
            mock_pbar.close.assert_called_once()
    
    def test_write_logs_in_chunks_with_smaller_actual_count(self):
        """Test when actual lines are fewer than expected."""
        app_name = "test-app"
        
        # Generator that yields fewer lines than expected
        def line_generator():
            for i in range(5):  # Only 5 lines instead of 10
                yield f"Test line {i+1}"
        
        # Configure for plain text
        self.mock_config.get_file_extension.return_value = ".log"
        self.writer.set_compress(False)
        
        # Should not raise an exception
        file_path = self.writer.write_logs_in_chunks(
            app_name, 
            line_generator(),
            total_lines=10,  # Expecting 10 lines
            chunk_size=2
        )
        
        # Verify file was created with correct content
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.read().splitlines()
            
        self.assertEqual(len(lines), 5)  # Only the actual lines written
        self.assertEqual(lines[0], "Test line 1")
        self.assertEqual(lines[4], "Test line 5")
    
    def test_compress_setting_overrides_extension(self):
        """Test that compression setting overrides file extension."""
        app_name = "test-app"
        log_lines = ["line1", "line2", "line3"]
        
        # Test case: Config has no .gz but compress=True
        self.mock_config.get_file_extension.return_value = ".log"
        self.writer.set_compress(True)
        
        file_path = self.writer.write_logs_to_file(app_name, log_lines)
        
        # Check that file IS gzipped despite no .gz extension in config
        self.assertTrue(file_path.endswith(".gz"))
        
        # Check contents with gzip
        with gzip.open(file_path, 'rt', encoding='utf-8') as f:
            content = f.read().strip().split('\n')
            self.assertEqual(content, log_lines)
    
    @patch('src.log_generator.LogGenerator')
    def test_estimate_lines_for_size_compressed(self, mock_generator_class):
        """
        Test estimating lines for target file size with compression.
        """
        # Set up mocks
        mock_generator = MagicMock()
        mock_generator_class.return_value = mock_generator
        
        # Mock generate_multiple_lines to return sample lines
        sample_line = "This is a sample log line that takes some space"
        mock_generator.generate_multiple_lines.return_value = [sample_line] * 5000  # Sample size
        
        # Test with target size 1MB and compression
        target_size_mb = 1
        compress = True
        
        with patch('os.path.getsize') as mock_getsize:
            # Mock file size to be 100KB (compressed)
            mock_getsize.return_value = 100 * 1024
            
            estimated = self.writer.estimate_lines_for_size("test-app", target_size_mb, compress)
            
            # Check for reasonable values based on compression-specific correction factor
            self.assertGreaterEqual(estimated, 10000)  # Minimum lines for files <= 50MB
            correction_factor = 1.05  # New compression factor
            expected_approx = int((1024 * 1024 / (100 * 1024)) * 5000 * correction_factor)
            self.assertAlmostEqual(estimated, expected_approx, delta=expected_approx * 0.1)  # Allow 10% margin

    @patch('src.log_generator.LogGenerator')
    def test_estimate_lines_for_size_uncompressed(self, mock_generator_class):
        """
        Test estimating lines for target file size without compression.
        """
        # Set up mocks
        mock_generator = MagicMock()
        mock_generator_class.return_value = mock_generator
        
        # Mock generate_multiple_lines to return sample lines
        sample_line = "This is a sample log line that takes some space"
        mock_generator.generate_multiple_lines.return_value = [sample_line] * 5000  # Sample size
        
        # Test with target size 1MB and without compression
        target_size_mb = 1
        compress = False
        
        with patch('os.path.getsize') as mock_getsize:
            # Mock file size to be 500KB (uncompressed)
            mock_getsize.return_value = 500 * 1024
            
            estimated = self.writer.estimate_lines_for_size("test-app", target_size_mb, compress)
            
            # Check for reasonable values based on non-compression correction factor
            self.assertGreaterEqual(estimated, 10000)  # Minimum lines for files <= 50MB
            correction_factor = 1.02  # New non-compression factor
            expected_approx = int((1024 * 1024 / (500 * 1024)) * 5000 * correction_factor)
            self.assertAlmostEqual(estimated, expected_approx, delta=expected_approx * 0.1)  # Allow 10% margin
            
    def test_exact_file_size(self):
        """
        Test that the generated file has an exact size (within margin).
        This is a more realistic test not using mocks.
        """
        app_name = "test-app"
        timestamp = datetime.datetime(2025, 1, 1, 12, 30, 45)
        
        # Create a test pattern for mock lines instead of using actual LogGenerator
        # This avoids dealing with timestamp configuration issues in mocks
        test_pattern = "This is a test log line with some consistent content to measure file size accurately"
        log_lines = [test_pattern] * 1000  # About 80 bytes per line × 1000 = ~80KB
        
        # Generate a small test file
        target_mb = 0.1  # 100KB target - small for quick tests
        target_size_bytes = int(target_mb * 1024 * 1024)
        
        # Test uncompressed file size accuracy
        self.writer.set_compress(False)
        
        # Calculate how many lines we need to get close to our target size
        # instead of using estimate_lines_for_size which depends on LogGenerator
        avg_line_bytes = len(test_pattern) + 1  # +1 for newline
        lines_needed = int(target_size_bytes / avg_line_bytes)
        log_lines = [test_pattern] * lines_needed
        
        file_path = self.writer.write_logs_to_file(app_name, log_lines, timestamp)
        
        # Check file size (within 5% of target)
        actual_size_bytes = os.path.getsize(file_path)
        
        self.assertAlmostEqual(
            actual_size_bytes, 
            target_size_bytes, 
            delta=target_size_bytes * 0.05,  # 5% margin
            msg=f"Expected ~{target_size_bytes} bytes, got {actual_size_bytes} bytes"
        )


if __name__ == '__main__':
    unittest.main()
