#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Unit tests for the cli module.
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import cli


class TestCLI(unittest.TestCase):
    """
    Test cases for CLI module.
    """
    
    def test_parse_args_defaults(self):
        """
        Test parsing command line arguments with defaults.
        """
        with patch('sys.argv', ['cli.py']):
            args = cli.parse_args()
            self.assertEqual(args.config, 'config.yaml')
            self.assertEqual(args.output_dir, 'output')
            self.assertEqual(args.app_name, 'apf-dsn-flow')
            self.assertIsNone(args.file_count)
            self.assertIsNone(args.size_mb)
            self.assertFalse(args.dry_run)
    
    def test_parse_args_custom(self):
        """
        Test parsing command line arguments with custom values.
        """
        test_args = [
            'cli.py',
            '--config', 'custom.yaml',
            '--output-dir', 'custom_output',
            '--app-name', 'custom-app',
            '--file-count', '50',
            '--size-mb', '200',
            '--dry-run'
        ]
        
        with patch('sys.argv', test_args):
            args = cli.parse_args()
            self.assertEqual(args.config, 'custom.yaml')
            self.assertEqual(args.output_dir, 'custom_output')
            self.assertEqual(args.app_name, 'custom-app')
            self.assertEqual(args.file_count, 50)
            self.assertEqual(args.size_mb, 200)
            self.assertTrue(args.dry_run)
    
    def test_parse_size_string(self):
        """
        Test parsing size string.
        """
        # Test MB
        self.assertEqual(cli.parse_size_string('100 MB'), 100)
        
        # Test GB
        self.assertEqual(cli.parse_size_string('2 GB'), 2048)
        
        # Test KB
        self.assertEqual(cli.parse_size_string('1024 KB'), 1)
        
        # Test small KB (min 1 MB)
        self.assertEqual(cli.parse_size_string('10 KB'), 1)
        
        # Test invalid format
        self.assertEqual(cli.parse_size_string('invalid'), 0)
        
        # Test empty string
        self.assertEqual(cli.parse_size_string(''), 0)
    
    @patch('cli.ConfigLoader')
    @patch('cli.LogGenerator')
    @patch('cli.LogWriter')
    def test_generate_logs_basic(self, mock_writer_class, mock_generator_class, mock_loader_class):
        """
        Test generate_logs function basic functionality.
        """
        # Set up mocks
        mock_config = MagicMock()
        mock_loader_class.return_value = mock_config
        mock_config.get_file_count.return_value = 10
        mock_config.get_file_size_after_compression.return_value = '50 MB'
        
        mock_generator = MagicMock()
        mock_generator_class.return_value = mock_generator
        
        mock_writer = MagicMock()
        mock_writer_class.return_value = mock_writer
        mock_writer.estimate_lines_for_size.return_value = 5000
        
        # Sample log lines
        mock_generator.generate_multiple_lines.return_value = ['line1', 'line2']
        
        # Run with basic parameters
        cli.generate_logs('test_config.yaml', 'test_output', 'test-app')
        
        # Verify correct calls
        mock_loader_class.assert_called_once_with('test_config.yaml')
        mock_generator_class.assert_called_once_with(mock_config)
        mock_writer_class.assert_called_once_with(mock_config)
        
        # Writer should be set up with base dir
        mock_writer.set_base_dir.assert_called_once_with('test_output')
        
        # Should estimate lines for file size
        mock_writer.estimate_lines_for_size.assert_called_once_with('test-app', 50)
        
        # Should generate multiple lines for each file
        self.assertEqual(mock_generator.generate_multiple_lines.call_count, 10)
        mock_generator.generate_multiple_lines.assert_called_with('test-app', 5000)
        
        # Should write logs to file for each file
        self.assertEqual(mock_writer.write_logs_to_file.call_count, 10)
    
    @patch('cli.ConfigLoader')
    @patch('cli.LogGenerator')
    @patch('cli.LogWriter')
    def test_generate_logs_override_params(self, mock_writer_class, mock_generator_class, mock_loader_class):
        """
        Test generate_logs function with override parameters.
        """
        # Set up mocks
        mock_config = MagicMock()
        mock_loader_class.return_value = mock_config
        
        mock_generator = MagicMock()
        mock_generator_class.return_value = mock_generator
        
        mock_writer = MagicMock()
        mock_writer_class.return_value = mock_writer
        mock_writer.estimate_lines_for_size.return_value = 5000
        
        # Run with override parameters
        cli.generate_logs('test_config.yaml', 'test_output', 'test-app', 
                         file_count=5, size_mb=20)
        
        # Should use overridden values
        mock_config.get_file_count.assert_not_called()
        mock_config.get_file_size_after_compression.assert_not_called()
        
        # Should estimate lines for overridden file size
        mock_writer.estimate_lines_for_size.assert_called_once_with('test-app', 20)
        
        # Should generate files with overridden count
        self.assertEqual(mock_generator.generate_multiple_lines.call_count, 5)
        self.assertEqual(mock_writer.write_logs_to_file.call_count, 5)
    
    @patch('cli.ConfigLoader')
    @patch('cli.LogGenerator')
    @patch('cli.LogWriter')
    def test_generate_logs_dry_run(self, mock_writer_class, mock_generator_class, mock_loader_class):
        """
        Test generate_logs function with dry run.
        """
        # Set up mocks
        mock_config = MagicMock()
        mock_loader_class.return_value = mock_config
        mock_config.get_file_count.return_value = 10
        mock_config.get_file_size_after_compression.return_value = '50 MB'
        
        # Run with dry run
        cli.generate_logs('test_config.yaml', 'test_output', 'test-app', dry_run=True)
        
        # Generator and writer should not be created
        mock_generator_class.assert_not_called()
        mock_writer_class.assert_not_called()


if __name__ == '__main__':
    unittest.main()
