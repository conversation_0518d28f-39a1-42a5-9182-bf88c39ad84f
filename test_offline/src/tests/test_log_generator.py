#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Unit tests for the log_generator module.
"""

import os
import re
import unittest
import datetime
from unittest.mock import patch, MagicMock

import sys

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from log_generator import LogGenerator


class TestLogGenerator(unittest.TestCase):
    """
    Test cases for LogGenerator class.
    """
    
    def setUp(self):
        """
        Set up test environment.
        """
        # Mock config
        self.mock_config = MagicMock()
        self.mock_config.get_timestamp_config.return_value = {
            'from': '2025-01-01 00:00:00',
            'to': '2025-01-02 23:59:59',
            'format': '%Y-%m-%d %H:%M:%S,%f'
        }
        self.mock_config.get_log_format.return_value = "__TIMESTAMP__ test [__UUID__] [__VAR1__:__VAR2__]"
        self.mock_config.get_variables.return_value = {
            "__VAR1__": ["value1", "value2"],
            "__VAR2__": ["A", "B", "C"]
        }
        
        # Create generator
        self.generator = LogGenerator(self.mock_config)
    
    def test_parse_timestamp_range(self):
        """
        Test parsing timestamp range.
        """
        self.assertEqual(self.generator.timestamp_from_dt, datetime.datetime(2025, 1, 1, 0, 0, 0))
        self.assertEqual(self.generator.timestamp_to_dt, datetime.datetime(2025, 1, 2, 23, 59, 59))
    
    def test_parse_timestamp_range_invalid(self):
        """
        Test parsing invalid timestamp range.
        """
        mock_config = MagicMock()
        mock_config.get_timestamp_config.return_value = {
            'from': 'invalid',
            'to': '2025-01-02 23:59:59',
            'format': '%Y-%m-%d %H:%M:%S,%f'
        }
        
        with self.assertRaises(ValueError):
            LogGenerator(mock_config)
    
    def test_generate_random_timestamp(self):
        """
        Test generating random timestamp.
        """
        timestamp = self.generator._generate_random_timestamp()
        self.assertIsInstance(timestamp, datetime.datetime)
        self.assertGreaterEqual(timestamp, self.generator.timestamp_from_dt)
        self.assertLessEqual(timestamp, self.generator.timestamp_to_dt)
    
    def test_format_timestamp(self):
        """
        Test formatting timestamp.
        """
        dt = datetime.datetime(2025, 1, 1, 12, 30, 45, 123456)
        formatted = self.generator._format_timestamp(dt)
        self.assertEqual(formatted, "2025-01-01 12:30:45,123456")
    
    def test_replace_variables(self):
        """
        Test replacing variables.
        """
        log_format = "__TIMESTAMP__ test [__UUID__] [__VAR1__:__VAR2__]"
        variables = {
            "__VAR1__": ["value1", "value2"],
            "__VAR2__": ["A", "B", "C"]
        }
        
        result = self.generator._replace_variables(log_format, variables)
        
        # Check timestamp
        self.assertIn(str(datetime.datetime.now().year), result)
        
        # Check UUID format
        uuid_pattern = r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        self.assertRegex(result, uuid_pattern)
        
        # Check variables
        self.assertIn("value", result)
        self.assertIn(":", result)
        
        # Verify that __UUID__ and __TIMESTAMP__ were replaced
        self.assertNotIn("__UUID__", result)
        self.assertNotIn("__TIMESTAMP__", result)
    
    def test_generate_log_line(self):
        """
        Test generating a log line.
        """
        line = self.generator.generate_log_line("test-app")
        
        # Verify mock was called with correct app name
        self.mock_config.get_log_format.assert_called_with("test-app")
        self.mock_config.get_variables.assert_called_with("test-app")
        
        # Check that the line contains the expected elements
        self.assertRegex(line, r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d+ test')
    
    def test_generate_multiple_lines(self):
        """
        Test generating multiple log lines.
        """
        lines = self.generator.generate_multiple_lines("test-app", 5)
        self.assertEqual(len(lines), 5)
        
        # All lines should have the basic format
        for line in lines:
            self.assertRegex(line, r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d+ test')
            
        # Lines should be different (due to UUID and random variables)
        self.assertNotEqual(lines[0], lines[1])


if __name__ == '__main__':
    unittest.main()
