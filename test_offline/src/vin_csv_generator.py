#!/usr/bin/env python3

import csv
import random
from typing import List, Dict, Any
from .config_loader import Config<PERSON>oader


class VinCsvGenerator:
    """Generator để tạo file CSV chứa VIN records."""
    
    def __init__(self, config: ConfigLoader):
        """
        Initialize VIN CSV generator.
        
        Args:
            config: ConfigLoader instance
        """
        self.config = config
        self.internal_vin_config = config.get_vin_config()
    
    def _generate_internal_vin(self, index: int) -> int:
        """
        Generate internal VIN based on index.

        Args:
            index: Sequential index for VIN generation

        Returns:
            Internal VIN as integer
        """
        start = int(self.internal_vin_config.get('internal_vin_start', 1))
        end = int(self.internal_vin_config.get('internal_vin_end', 9999999))

        # Ensure index is within range
        vin_number = start + (index % (end - start + 1))
        return vin_number
    
    def _generate_vin_from_internal(self, internal_vin: int) -> str:
        """
        Generate VIN from internal VIN.

        Args:
            internal_vin: Internal VIN integer

        Returns:
            Full VIN string
        """
        return f"VIN{internal_vin}"
    
    def _get_random_value(self, values: List[Any]) -> Any:
        """
        Get random value from list.
        
        Args:
            values: List of possible values
            
        Returns:
            Random value from list
        """
        if not values:
            return None
        return random.choice(values)
    
    def generate_csv(self, output_file: str, record_count: int = None) -> None:
        """
        Generate CSV file with VIN records.

        Args:
            output_file: Output CSV file path
            record_count: Number of records to generate (if None, calculate from range)
        """
        columns_config = self.internal_vin_config.get('columns', {})

        # Calculate record count from internal_vin range if not specified
        if record_count is None:
            start = int(self.internal_vin_config.get('internal_vin_start', 1))
            end = int(self.internal_vin_config.get('internal_vin_end', 9999999))
            record_count = end - start + 1

        # Get column names
        column_names = list(columns_config.keys())

        print(f"Generating {record_count:,} VIN records to {output_file}")
        print(f"Internal VIN range: {self.internal_vin_config.get('internal_vin_start')} - {self.internal_vin_config.get('internal_vin_end')}")
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # Write header
            writer.writerow(column_names)

            # Generate records
            for i in range(record_count):
                if (i + 1) % 10000 == 0:
                    print(f"Generated {i + 1:,} records...")

                record = {}
                internal_vin = self._generate_internal_vin(i)

                for column_name, column_config in columns_config.items():
                    if column_config == "__INTERNAL_VIN__":
                        record[column_name] = internal_vin
                    elif column_config == "VIN__INTERNAL_VIN__":
                        record[column_name] = self._generate_vin_from_internal(internal_vin)
                    elif column_config == "NULL":
                        record[column_name] = "NULL"
                    elif isinstance(column_config, list):
                        record[column_name] = self._get_random_value(column_config)
                    else:
                        record[column_name] = column_config

                # Write row in correct column order
                row = [record[col] for col in column_names]
                writer.writerow(row)
        
        print(f"Successfully generated {record_count:,} records in {output_file}")


def main():
    """Main function to run VIN CSV generator."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Generate VIN CSV file')
    parser.add_argument('--config', type=str, default='config.yaml',
                        help='Configuration file path (default: config.yaml)')
    parser.add_argument('--output', type=str, default='vin_data.csv',
                        help='Output CSV file path (default: vin_data.csv)')
    parser.add_argument('--count', type=int, default=None,
                        help='Number of records to generate (default: calculate from internal_vin range)')

    args = parser.parse_args()

    # Load configuration
    config = ConfigLoader(args.config)

    # Create generator
    generator = VinCsvGenerator(config)

    # Generate CSV
    generator.generate_csv(args.output, args.count)


if __name__ == "__main__":
    main()
