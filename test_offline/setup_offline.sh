#!/bin/bash
# Script setup cho máy offline

echo "=== Setup Log Generator Offline ==="

# Kiểm tra Python
if ! command -v python3 &> /dev/null; then
    echo "Lỗi: Python3 không được cài đặt"
    exit 1
fi

# Tạo virtual environment
echo "Tạo virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install packages từ offline cache
echo "Cài đặt packages từ offline cache..."
pip install --no-index --find-links offline/pip_packages -r requirements.txt

echo "Setup hoàn tất!"
echo "Để sử dụng:"
echo "1. source venv/bin/activate"
echo "2. python -m src.cli --help"
