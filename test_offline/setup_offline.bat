@echo off
REM Script setup cho Windows offline

echo === Setup Log Generator Offline ===

REM Kiểm tra Python
python --version >nul 2>&1
if errorlevel 1 (
    echo Lỗi: Python không đượ<PERSON> cài đặt
    exit /b 1
)

REM Tạo virtual environment
echo Tạo virtual environment...
python -m venv venv
call venv\Scripts\activate.bat

REM Install packages từ offline cache
echo Cài đặt packages từ offline cache...
pip install --no-index --find-links offline\pip_packages -r requirements.txt

echo Setup hoàn tất!
echo Để sử dụng:
echo 1. venv\Scripts\activate.bat
echo 2. python -m src.cli --help
