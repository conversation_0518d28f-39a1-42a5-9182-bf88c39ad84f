#!/bin/bash

# Script để chạy log generator

# <PERSON><PERSON>m bảo Python và các dependencies đã được cài đặt
command -v python3 >/dev/null 2>&1 || { echo >&2 "Python 3 không được tìm thấy. H<PERSON>y cài đặt trước."; exit 1; }
pip3 install -q pyyaml || { echo >&2 "Không thể cài đặt dependency. Vui lòng kiểm tra lại."; exit 1; }

# Lấy đường dẫn thư mục hiện tại
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Chuyển đến thư mục của project
cd "$DIR"

echo "Log Generator - Công cụ sinh file log"
echo "========================================="

# Mặc định tham số
CONFIG="config.yaml"
OUTPUT="output"
APP="apf-dsn-flow"
FILE_COUNT=""
SIZE_MB=""
DRY_RUN=false

# Parse các tham số
while [[ $# -gt 0 ]]; do
  case $1 in
    --config)
      CONFIG="$2"
      shift 2
      ;;
    --output)
      OUTPUT="$2"
      shift 2
      ;;
    --app)
      APP="$2"
      shift 2
      ;;
    --files)
      FILE_COUNT="$2"
      shift 2
      ;;
    --size)
      SIZE_MB="$2"
      shift 2
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    --help)
      echo "Sử dụng: $0 [options]"
      echo "Options:"
      echo "  --config FILE    Đường dẫn đến file cấu hình YAML (mặc định: config.yaml)"
      echo "  --output DIR     Thư mục output (mặc định: output)"
      echo "  --app NAME       Tên ứng dụng trong file cấu hình (mặc định: apf-dsn-flow)"
      echo "  --files COUNT    Số lượng file log cần tạo (ghi đè cấu hình)"
      echo "  --size MB        Kích thước mỗi file sau khi nén, MB (ghi đè cấu hình)"
      echo "  --dry-run        Chạy thử, không tạo file"
      echo "  --help           Hiển thị thông tin trợ giúp này"
      exit 0
      ;;
    *)
      echo "Tham số không hợp lệ: $1"
      echo "Dùng --help để xem các tùy chọn"
      exit 1
      ;;
  esac
done

# Xây dựng command
CMD="python3 src/cli.py --config $CONFIG --output-dir $OUTPUT --app-name $APP"

if [ -n "$FILE_COUNT" ]; then
  CMD="$CMD --file-count $FILE_COUNT"
fi

if [ -n "$SIZE_MB" ]; then
  CMD="$CMD --size-mb $SIZE_MB"
fi

if [ "$DRY_RUN" = true ]; then
  CMD="$CMD --dry-run"
fi

# Thực thi command
echo "Đang chạy: $CMD"
eval $CMD

# Kiểm tra kết quả
if [ $? -eq 0 ]; then
  echo "Hoàn tất sinh log!"
else
  echo "Lỗi khi sinh log. Vui lòng kiểm tra lại các tham số và thử lại."
  exit 1
fi
